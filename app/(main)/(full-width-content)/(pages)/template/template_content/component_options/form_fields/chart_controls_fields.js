import {
  ExpandOutlined,
  DoubleLeftOutlined,
  DoubleRightOutlined,
  RotateRightOutlined,
  BgColorsOutlined,
  ExportOutlined,
  LoginOutlined,
} from "@ant-design/icons";

/**
 * Form fields for Chart Controls component option
 */
export const ChartControlsFields = {
  topSpacer: {
    type: "checkbox",
    inputType: "string",
    label: "Top Spacer",
    value: " ",
  },
  binTypeToggle: {
    type: "checkbox",
    inputType: "radioButton",
    label: "Bin Type Toggle",
    onChange: "toggleBinType",
    dataKey: "bin_type",
    position: ["top", "center"],
    options: [
      {
        label: "Software",
        value: "soft",
      },
      {
        label: "Hardware",
        value: "hard",
      },
    ],
    hideNoData: false,
    className: "mb-2",
  },
  zonalTypeToggle: {
    type: "checkbox",
    inputType: "radioButton",
    label: "Zonal Type Toggle",
    defaultValue: "percent_zonal",
    onChange: "toggleZonalType",
    position: ["top", "center"],
    options: [
      {
        label: "Percent Zonal",
        value: "percent_zonal",
      },
      {
        label: "Number Zonal",
        value: "number_zonal",
      },
    ],
    hideNoData: false,
  },
  fullScreen: {
    type: "checkbox",
    inputType: "button",
    label: "Full Screen",
    icon: <ExpandOutlined />,
    onClick: "toggleFullScreen",
    position: ["bottom", "right"],
  },
  waferIdSelection: {
    type: "checkbox",
    inputType: "select",
    label: "Wafer ID Selection",
    placeholder: "-Select Wafer ID-",
    popupMatchSelectWidth: false,
    labelInValue: true,
    onChange: "setWaferIdSelection",
  },
  previousWaferId: {
    type: "checkbox",
    inputType: "button",
    label: "Prev Wafer ID",
    icon: <DoubleLeftOutlined />,
    onClick: "showPreviousWaferId",
  },
  nextWaferId: {
    type: "checkbox",
    inputType: "button",
    label: "Next Wafer ID",
    icon: <DoubleRightOutlined />,
    onClick: "showNextWaferId",
  },
  tooltip: {
    type: "checkbox",
    inputType: "switch",
    label: "Tooltip",
    defaultChecked: true,
    checkedChildren: "ON",
    unCheckedChildren: "OFF",
    position: ["bottom", "left"],
    onChange: "toggleTooltip",
    tooltip: "An informational hovercard will popup when you mouseover",
  },
  orientationSettings: {
    type: "checkbox",
    inputType: "button",
    label: "Orientation Settings",
    icon: <RotateRightOutlined />,
    position: ["bottom", "left"],
    onClick: "showOrientationSettings",
  },
  colorScale: {
    type: "checkbox",
    inputType: "button",
    label: "Color Scale",
    icon: <BgColorsOutlined />,
    position: ["bottom", "left"],
    onClick: "showColorScaleOptions",
  },
  viewAllWaferIds: {
    type: "checkbox",
    inputType: "button",
    label: "View all wafer IDs",
    icon: <ExportOutlined />,
    position: ["bottom", "left"],
    onClick: "viewWaferMapGallery",
  },
  viewPinYieldDatalogBreakdown: {
    type: "checkbox",
    inputType: "button",
    label: "View all Datalog Breakdown",
    icon: <ExportOutlined />,
    position: ["bottom", "left"],
    onClick: "viewPinYieldDatalogBreakdown",
    renderConditionAction: "hasDatalogBreakdown",
  },
  viewMeanParametricPinSubstrateDatalogBreakdown: {
    type: "checkbox",
    inputType: "button",
    label: "View all Datalog Breakdown",
    icon: <ExportOutlined />,
    position: ["bottom", "left"],
    onClick: "viewMeanParametricPinSubstrateDatalogBreakdown",
    renderConditionAction: "hasDatalogBreakdown",
  },
  viewPinYieldGroupBreakdown: {
    type: "checkbox",
    inputType: "button",
    label: "View all Group Breakdown",
    icon: <ExportOutlined />,
    position: ["bottom", "left"],
    onClick: "viewPinYieldGroupBreakdown",
    renderConditionAction: "hasGroupBreakdown",
  },
  viewMeanParametricPinSubstrateGroupBreakdown: {
    type: "checkbox",
    inputType: "button",
    label: "View all Group Breakdown",
    icon: <ExportOutlined />,
    position: ["bottom", "left"],
    onClick: "viewMeanParametricPinSubstrateGroupBreakdown",
    renderConditionAction: "hasGroupBreakdown",
  },
  openSelectedTestAnalysis: {
    type: "checkbox",
    inputType: "button",
    label: "Open New Analysis",
    icon: <LoginOutlined />,
    position: ["bottom", "left"],
    onClick: "openSelectedTestAnalysis",
    tooltip: "This will open a new Selected Test",
    renderConditionAction: "hasOpenSelectedTestAnalysisOption",
  },
  showReprobe: {
    type: "checkbox",
    inputType: "switch",
    label: "Show Reprobe",
    defaultChecked: true,
    checkedChildren: "ON",
    unCheckedChildren: "OFF",
    position: ["bottom", "left"],
    onChange: "toggleReprobeXY",
    tooltip: "Show/hide the map's reprobe crosses",
  },
};
