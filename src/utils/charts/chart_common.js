import Highcharts from "highcharts";
import Helper from "../helper";

/**
 * Generate the histogram and scatter(hidden) series
 *
 * @param {array} data
 * @param {object} settings
 * @param {array} legend
 * @param {array} seriesColors
 * @param {string} histogramType
 * @returns {array} series
 */
export const getHistogramDataSeries = (
  data = [],
  settings = {},
  legend = [],
  seriesColors = [],
  histogramType = "bar",
) => {
  const series = [];
  // Add series for the grouped dataset
  data.forEach((seriesData, seriesIndex) => {
    // Just to be compatible with what's already implemented of having one dataset only
    // Should have appropriate naming when there's a grouped dataset already
    let seriesName = settings.histogram?.title ?? "";
    if (legend[seriesIndex]) {
      seriesName = legend[seriesIndex];
    } else if (data.length > 1) {
      seriesName = `Series ${seriesIndex}`;
    }
    const seriesOptions = {
      name: seriesName,
      data: seriesData,
      id: `h${seriesIndex}`,
      color:
        seriesColors[seriesIndex] ??
        Highcharts.getOptions().colors[seriesIndex],
    };
    if (histogramType === "bar") {
      seriesOptions.type = "column";
    } else {
      seriesOptions.type = "spline";
      seriesOptions.marker = {
        radius: 2,
      };
    }
    series.push(seriesOptions);
  });

  return series;
};

/**
 * Get scatter and a single line series
 *
 * @param {array} scatterData
 * @param {array} lineData
 * @param {array} seriesNames
 * @param {array} tooltipData
 * @param {boolean} boost
 * @returns {array} series
 */
export const getScatterLineSeries = (
  scatterData = [],
  lineData = [],
  seriesNames = [],
  tooltipData = {},
  boost = false,
) => {
  const series = [];
  scatterData.forEach((seriesData, seriesIndex) => {
    const name =
      typeof seriesNames === "string"
        ? seriesNames
        : (seriesNames[seriesIndex] ?? `Dataset ${seriesIndex}`);
    series.push({
      type: "scatter",
      name: name,
      data: seriesData,
      marker: {
        enabled: true,
      },
      tooltip: {
        pointFormatter: function () {
          return `
            Part ID: ${tooltipData[seriesIndex][this.index][0]} <br />
            Result: ${tooltipData[seriesIndex][this.index][1]} <br />
            Probability %: ${Helper.numberFormat(tooltipData[seriesIndex][this.index][2], 2)}% <br />
            Standard Quartile: ${Helper.numberFormat(tooltipData[seriesIndex][this.index][3], 4)} <br />
            Bin #: ${tooltipData[seriesIndex][this.index][4]}
          `;
        },
      },
      boostThreshold: boost ? 1 : 2000,
    });
  });

  if (lineData.length) {
    series.push({
      type: "line",
      name: "Normal Probability Line",
      color: "#00FF00",
      lineWidth: 2,
      yAxis: 1,
      data: lineData,
      marker: {
        enabled: false,
      },
      enableMouseTracking: false,
    });
  }

  return series;
};

/**
 * Draw the spline (curve) series of the histogram
 *
 * @param {object} chart
 * @param {object} settings
 */
export const drawSplineSeries = (chart, settings) => {
  chart.shouldDrawSpline = false;
  const histogramIds = chart.userOptions.series
    .filter((series) => series.type == "histogram")
    .map((series) => series.id);
  histogramIds.forEach((histogramId, idx) => {
    const histogram = chart.get(histogramId);
    // Show histogram series since points data will not be available for hidden series
    histogram.show();
    const total = chart.shouldDrawPercentHistogram
      ? histogram
          .getColumn("y")
          .reduce((accumulator, currentValue) => accumulator + currentValue, 0)
      : 0;
    const histogramData = histogram.points.map((point) => {
      return {
        x: point.x + (point.x2 - point.x) / 2,
        y:
          chart.shouldDrawPercentHistogram && total !== 0
            ? (point.y / total) * 100
            : point.y,
        hx: point.x,
        hx2: point.x2,
      };
    });
    // Sort x data into ascending order to avoid Highcharts warning #15: www.highcharts.com/errors/15/
    histogramData.sort((a, b) => a.x - b.x);
    // Hide histogram series after getting points data
    histogram.hide();

    let seriesName = histogram.name ?? settings.histogram?.title ?? "";
    // Draw curve histogram
    chart.addSeries({
      name: seriesName,
      type: "spline",
      color: Highcharts.getOptions().colors[idx],
      data: histogramData,
      yAxis: 0,
      xAxis: 2,
      marker: {
        enabled: false,
      },
      tooltip: {
        headerFormat: "",
        pointFormatter() {
          const { hx, hx2 } = this.options;
          const headerFormat = `<span style="font-size: 10px">${hx} - ${hx2}</span><br/>`;
          const pointFormat = `<span style="color:${this.color}">\u25CF</span> ${this.series.name}: <b>${chart.shouldDrawPercentHistogram ? `${Helper.numberFormat(this.y, 2)}%` : this.y}</b><br/>`;

          return headerFormat + pointFormat;
        },
      },
    });
  });
};

/**
 * Get boxplot series
 *
 * @param {array} data
 * @param {array} seriesNames
 * @param {array} boxplotStats
 * @param {array} categories
 * @returns {array} series
 */
export const getBoxPlotSeries = (
  data = [],
  seriesNames = [],
  boxplotStats = [],
  categories = [],
) => {
  const series = [];
  data.forEach((seriesData, seriesIndex) => {
    series.push({
      name: seriesNames[seriesIndex] ?? `Boxplot Series ${seriesIndex}`,
      data: seriesData,
      tooltip: {
        headerFormat: "",
        pointFormatter: function () {
          return `<strong>${categories[this.index]}</strong><br>
              Max Result: ${Helper.numberFormat(this.high, 4)}<br>
              Upper Quartile: ${Helper.numberFormat(this.q3, 4)}<br>
              Median: ${Helper.numberFormat(this.median, 4)}<br>
              Lower Quartile: ${Helper.numberFormat(this.q1, 4)}<br>
              Min Result: ${Helper.numberFormat(this.low, 4)}<br>
              Mean: ${Helper.numberFormat(
                boxplotStats[seriesIndex][this.index].mean,
                4,
              )}<br>
              Standard Deviation: ${Helper.numberFormat(
                boxplotStats[seriesIndex][this.index].stdev,
                4,
              )}`;
        },
      },
    });
  });

  return series;
};

/**
 * Get scatter series
 *
 * @param {array} data
 * @param {array} seriesNames
 * @return {array} series
 */
export const getScatterSeries = (data = [], seriesNames = []) => {
  const series = [];
  data.forEach((seriesData, seriesIndex) => {
    series.push({
      type: "scatter",
      name: seriesNames[seriesIndex] ?? `Group ${seriesIndex}`,
      data: seriesData,
    });
  });

  return series;
};

/**
 * Get line series
 *
 * @param {array} data
 * @param {array} seriesNames
 * @returns {array} series
 */
export const getLineSeries = (data = [], seriesNames = []) => {
  const series = [];
  data.forEach((seriesData, seriesIndex) => {
    series.push({
      name: seriesNames[seriesIndex] ?? `Dataset ${seriesIndex}`,
      data: seriesData,
      tooltip: {
        pointFormatter: function () {
          return `<b>${this.series.name}</b>: ${Helper.numberFormat(this.y, 2)}`;
        },
      },
    });
  });

  return series;
};

/**
 * Get subtitle for charts plotting with test results
 *
 * @param {object} data
 * @param {object} additionalData
 * @returns {string} text
 */
export const getResultsSubtitle = (data, additionalData) => {
  let text = "";
  if (Object.keys(data).length > 0) {
    text = `Test # ${data.actual_test_number} :: ${data.test_name} (${data.test_unit || data.test_unit !== "" ? data.test_unit : "-"}) <br/> Test Limits [${data.lo_lim ?? "-"}, ${data.hi_lim ?? "-"}]`;
  }
  if (additionalData?.stats_type_display) {
    text += ` :: ${additionalData.stats_type_display}`;
  }
  if (data.new_lo_lim_valid || data.new_hi_lim_valid) {
    text += `<br/> New Test Limits [${data.new_lo_lim_valid && data.new_lo_lim !== undefined ? Helper.descriptiveFormatting(data.new_lo_lim, 4, 4) : "-"}, ${data.new_hi_lim_valid && data.new_hi_lim !== undefined ? Helper.descriptiveFormatting(data.new_hi_lim, 4, 4) : "-"}]`;
  }

  return text;
};

/**
 * Set the min and max value of an axis
 *
 * @param {object} axis
 * @param {object} data
 * @param {boolean} shouldUseLimits
 */
export const setAxisMinMax = (axis, data, shouldUseLimits = true) => {
  const { min_result, lo_lim, max_result, hi_lim } = data;
  axis.min = shouldUseLimits ? Math.min(min_result, lo_lim) : min_result;
  axis.max = shouldUseLimits ? Math.max(max_result, hi_lim) : max_result;
};

/**
 * Set the plot bands of an axis
 *
 * @param {object} plotBandData
 * @param {object} options
 */
export const setPlotBands = (plotBandData, options) => {
  if (plotBandData?.length) {
    const xPlotBands = [],
      yPlotBands = [];

    plotBandData.forEach((plotBand) => {
      if (plotBand?.axis === "x") {
        xPlotBands.push({
          color: plotBand?.color ?? "#FCFFC5",
          from: plotBand.from,
          to: plotBand.to,
        });
      } else if (plotBand?.axis === "y") {
        yPlotBands.push({
          color: plotBandData?.color ?? "#FCFFC5",
          from: plotBand.from,
          to: plotBand.to,
        });
      }
    });

    if (xPlotBands.length) {
      options.xAxis[0].plotBands = xPlotBands;
    } else if (yPlotBands.length) {
      options.yAxis[0].plotBands = xPlotBands;
    }
  }
};

/**
 * Generate grouped series legend
 *
 * @param {object} chart
 * @param {object} chartCustomData
 */
export const setGroupedSeriesLegend = (chart, chartCustomData) => {
  const renderer = chart.renderer;
  const markerSize = 12;
  const rowSpacing = 8;
  const padding = 10;

  // remove existing grouped legend if present
  if (chartCustomData.groupedLegendElement) {
    chartCustomData.groupedLegendElement.forEach((element) => {
      element.remove();
    });
  }

  // group series by group name
  const groups = {};
  chart.series.forEach((series) => {
    const groupName = series.userOptions.group || series.name;
    if (!groups[groupName])
      groups[groupName] = {
        color: series.userOptions.groupColor || series.color,
        seriesList: [],
      };
    groups[groupName].seriesList.push(series);
  });

  // measure item widths
  const items = Object.keys(groups).map((groupName) => {
    const tempText = renderer
      .text(groupName, -9999, -9999)
      .css({ fontSize: "12px" })
      .add();
    const width = tempText.getBBox().width + markerSize + padding;
    tempText.destroy();
    const group = groups[groupName];
    return {
      groupName,
      color: group.color,
      seriesList: group.seriesList,
      width,
    };
  });

  // layout with wrapping
  const maxWidth = chart.plotWidth;
  const rows = [];
  let currentRow = [];
  let rowWidth = 0;

  items.forEach((item) => {
    if (rowWidth + item.width > maxWidth && currentRow.length) {
      rows.push(currentRow);
      currentRow = [];
      rowWidth = 0;
    }
    currentRow.push(item);
    rowWidth += item.width + padding;
  });
  if (currentRow.length) rows.push(currentRow);

  const rowHeight = 20;
  const legendTop =
    chart.chartHeight -
    (rows.length * rowHeight + (rows.length - 1) * rowSpacing);
  const groupedLegendElement = renderer.g("grouped-legend").add();
  let firstRowX = 0;

  rows.forEach((rowItems, rowIndex) => {
    if (rowIndex === 0) {
      const totalRowWidth = rowItems.reduce(
        (sum, i) => sum + i.width + padding,
        0,
      );
      firstRowX = chart.plotLeft + (chart.plotWidth - totalRowWidth) / 2;
    }
    let x = firstRowX;
    const y = legendTop + rowIndex * (rowHeight + rowSpacing);

    rowItems.forEach(({ groupName, color, seriesList, width }) => {
      const isVisible = seriesList.some((s) => s.visible);

      const marker = renderer
        .rect(x, y, markerSize, markerSize, 2)
        .attr({
          fill: isVisible ? color : "#ccc",
          stroke: "#000",
          "stroke-width": 1,
        })
        .css({ cursor: "pointer" })
        .add(groupedLegendElement);

      const label = renderer
        .text(groupName, x + markerSize + 5, y + markerSize - 1)
        .css({
          fontSize: "12px",
          color: isVisible ? "#333" : "#aaa",
          cursor: "pointer",
        })
        .add(groupedLegendElement);

      const toggleGroup = () => {
        const shouldHide = seriesList.some((s) => s.visible);
        seriesList.forEach((s) => s.setVisible(!shouldHide, false));
        chart.redraw();
        marker.attr({ fill: shouldHide ? "#ccc" : color });
        label.css({ color: shouldHide ? "#aaa" : "#333" });
      };

      const hover = () => {
        seriesList.forEach((s) => s.graph?.attr({ "stroke-width": 4 }));
        label.css({ fontWeight: "bold", color: "#000" });
      };

      const unhover = () => {
        seriesList.forEach((s) => s.graph?.attr({ "stroke-width": 2 }));
        label.css({
          fontWeight: "normal",
          color: seriesList.some((s) => s.visible) ? "#333" : "#aaa",
        });
      };

      [marker, label].forEach((el) => {
        el.on("click", toggleGroup);
        el.on("mouseover", hover);
        el.on("mouseout", unhover);
      });

      x += width + padding;
    });
  });

  if (!chartCustomData.originalChartHeight) {
    chartCustomData.originalChartHeight = chart.chartHeight;
  }
  const legendBBox = groupedLegendElement.getBBox();
  const legendHeight = legendBBox.height;
  const expectedChartHeight =
    chartCustomData.originalChartHeight + legendHeight + 10;

  // only update if chart height is not equal to expected chart height to avoid infinite render loop
  if (chart.chartHeight !== expectedChartHeight) {
    chartCustomData.heightAdjusted = true;

    const spacingPadding = 30; // space between legend and X-axis title
    chart.update(
      {
        chart: {
          height: expectedChartHeight + spacingPadding - 10,
          spacingBottom: legendHeight + spacingPadding,
        },
      },
      false,
    );
  }

  chartCustomData.groupedLegendElement = chart.container.querySelectorAll(
    ".highcharts-grouped-legend",
  );
};

/**
 * Redraw chart when in boost mode to fix issue where series are not drawn
 *
 * @param {object} chart
 */
export const redrawWhenBoosted = (chart) => {
  const seriesBoosted = chart.series.some((s) => s.boosted === true);
  if (chart.boosted || seriesBoosted) {
    setTimeout(() => {
      if (chart.series) {
        chart.redraw();
      }
    }, 0);
  }
};
