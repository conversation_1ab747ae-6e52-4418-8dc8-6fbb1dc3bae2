"use client";

import { App, Empty, notification } from "antd";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/highcharts-more";
import "highcharts/modules/no-data-to-display";
import "highcharts/modules/exporting";
import "highcharts/modules/offline-exporting";
import "highcharts/modules/accessibility";
import "highcharts/modules/boost";
import { useEffect, useRef, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { merge } from "lodash";
import Api from "../api";
import Helper from "../helper";
import { useBoundStore } from "../../store/store";
import { QueryKeys } from "../query_keys";
import { useChartDefaultOptions } from "../../hooks/useChartDefaultOptions";
import { DEFAULT_PLOTLINES } from "./constants";
import ChartHelper from "./chart_helper";
import {
  getScatterLineSeries,
  getResultsSubtitle,
  setAxisMinMax,
  redrawWhenBoosted,
} from "./chart_common";
import ChartLoading from "./loading";

// stats to be displayed below the chart
const stats = {
  ...ChartHelper.getChartDefaultStats(),
  ...{
    p_value: "P-Value",
  },
};

/**
 * Normal Probability chart
 *
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @param {object} chartFilters
 * @param {function} setIsChartBoosted
 * @param {function} setHasChartData
 * @param {function} setHighchartsChart
 * @param {object} fullScreenHandle
 * @param {boolean} hasTopFailingTests
 * @param {object} localChartData
 * @param {boolean} boostMode
 * @param {object} prerenderData
 * @returns {JSX.Element}
 */
export default function NormalProbability({
  chartRef,
  component,
  filters,
  pageKey,
  chartKey,
  chartCustomData,
  chartFilters = {},
  setIsChartBoosted,
  setHasChartData,
  setHighchartsChart,
  fullScreenHandle,
  hasTopFailingTests: hasTopFailingTestsProp = true,
  localChartData,
  boostMode = true,
  prerenderData = {},
}) {
  const [chartOptions, setChartOptions] = useState();
  const [topFailingTestData, setTopFailingTestData] = useState();
  const [shouldFetchChartData, setShouldFetchChartData] = useState(false);
  const urlParams = useBoundStore((state) => state.urlParams);
  const cacheData = useBoundStore((state) => state.cacheData);
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const setCurrentChart = useBoundStore((state) => state.setCurrentChart);
  const retainedChartOptions = useBoundStore(
    (state) => state.retainedChartOptions,
  );
  const chartData = useRef({});
  const { message } = App.useApp();
  const [notificationApi, contextHolder] = notification.useNotification();
  const queryClient = useQueryClient();
  const params = component.props.params;
  const settings = component.props.settings;
  const chartType = component.name;
  const hasTopFailingTests =
    settings.has_top_failing_tests ?? hasTopFailingTestsProp;
  const boost = settings.boost_mode ?? boostMode;

  const options = merge(ChartHelper.getChartDefaultSettings(true, boost), {
    chart: {
      events: {
        fullscreenOpen: () => {
          chartCustomData[chartKey].shouldSetStatsInfoPosition = true;
          chartCustomData[chartKey].isFullScreen = true;
        },
        fullscreenClose: () => {
          chartCustomData[chartKey].shouldSetStatsInfoPosition = true;
          chartCustomData[chartKey].isFullScreen = false;
        },
        render: (event) => {
          const chart = event.target;
          if (
            chartCustomData?.[chartKey]?.shouldSetStatsInfoPosition ||
            chart.shouldRenderStatsInfo
          ) {
            chartCustomData[chartKey].shouldSetStatsInfoPosition = false;
            chart.shouldRenderStatsInfo = false;
            ChartHelper.renderStatsInfoToChart(
              chart,
              stats,
              chartData.current,
              options,
              chartCustomData[chartKey],
            );
          }
          if (typeof setIsChartBoosted === "function") {
            setIsChartBoosted(chart.boosted);
          }
        },
      },
    },
    title: {
      text: settings.show_title ? (chartFilters.title ?? settings.title) : "",
      align: "left",
    },
    subtitle: {},
    xAxis: [
      {
        title: {
          text: settings.x.title,
        },
        labels: {
          formatter: function () {
            const label = !isNaN(Number(this.value))
              ? Helper.numberFormat(this.value, 4)
              : this.axis.defaultLabelFormatter.call(this);
            return label;
          },
        },
        max: settings.x.max ?? null,
        min: settings.x.min ?? null,
      },
    ],
    yAxis: [
      {
        title: {
          text: settings.y.title,
        },
        max: settings.y.max ?? null,
        min: settings.y.min ?? null,
      },
      {
        title: {
          text: settings.y2.title,
        },
        opposite: true,
        max: settings.y2.max ?? null,
        min: settings.y2.min ?? null,
        linkedTo: 0,
        gridLineWidth: 0,
        labels: {},
      },
    ],
    exporting: {
      chartOptions: {
        chart: {
          events: {
            load: () => {
              chartCustomData[chartKey].shouldSetStatsInfoPosition = true;
              chartCustomData[chartKey].shouldRemoveStatsInfoElement = false;
            },
          },
        },
      },
    },
    boost: {
      useGPUTranslations: true,
    },
    series: [],
    yhDefinedConfig: {
      name: Helper.titlelize(chartType),
      chartType: chartType,
      chartKey: chartKey,
      resultsAxis: "x",
      logScaleAxis: "x",
      hasStatsInfo: true,
      class: "normalProbability",
      default: settings,
    },
  });

  useEffect(() => {
    if (typeof Highcharts !== "object") {
      message.warning("Highcharts not loaded.", 10);
    }

    return () => {
      queryClient.removeQueries({
        queryKey: QueryKeys.chart(pageKey, chartKey),
      });
    };
  }, []);

  useEffect(() => {
    if (hasTopFailingTests) {
      if (component.props.params.body_params.test_number !== undefined) {
        setTopFailingTestData({
          test_number: component.props.params.body_params.test_number,
        });
      } else {
        Helper.getLotTopFailingTestData(
          urlParams[pageKey].src_type,
          urlParams[pageKey].src_value,
          urlParams[pageKey].mfg_process,
          setTopFailingTestData,
          message.warning,
          message.error,
          cacheData,
        );
      }
    }
  }, []);

  useEffect(() => {
    if (hasTopFailingTests) {
      setShouldFetchChartData(topFailingTestData !== undefined);
    } else {
      setShouldFetchChartData(true);
    }
  }, [topFailingTestData]);

  /**
   * Fetch chart data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchChartData = async ({ signal }) => {
    chartRef.current?.chart?.showLoading();

    if (typeof topFailingTestData?.test_number !== "undefined") {
      filters[pageKey].tNum = topFailingTestData.test_number;
    }
    let allFilters = { ...filters[pageKey], ...chartFilters, ...prerenderData };
    const url = Helper.parseUrlEndpoint(params.url_endpoint, allFilters);
    const requestParams = Helper.filterObjectByKeys(
      allFilters,
      Object.keys(params.body_params),
    );

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Fetch the default chart options of this chart type
   */
  const defaultChartOptions = useChartDefaultOptions(chartType);

  /**
   * Query to fetch chart data
   */
  const chartDataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey, filters),
    queryFn: fetchChartData,
    enabled:
      shouldFetchChartData &&
      defaultChartOptions.isSuccess &&
      typeof localChartData === "undefined",
  });

  /**
   * Set chart options after all required data are available
   *
   * @param {object} data
   */
  const setOptions = (data) => {
    const scatterData = data.qq_plot_data ?? data.scatter_data;
    if (typeof setHasChartData === "function") {
      setHasChartData(Array.isArray(scatterData) && scatterData.length > 0);
    }
    chartData.current = data;
    options.subtitle.text = getResultsSubtitle(data, filters[pageKey]);
    options.series = getScatterLineSeries(
      scatterData,
      data.line_data,
      data?.x_categories ?? data?.chart_legend ?? data?.legend,
      data.tooltip_details,
      boost,
    );
    setAxisMinMax(
      options.xAxis[0],
      data,
      settings.should_use_limits_scaling ?? true,
    );
    options.xAxis[0].plotLines = [
      ...ChartHelper.getPlotLines(
        data,
        ChartHelper.filterPlotLines(DEFAULT_PLOTLINES, chartFilters),
        true,
      ),
      ...ChartHelper.getSigmaLines(data, true),
    ];
    // Append the test data to the user options
    options.testData = ChartHelper.setTestDataToUserOptions(data);
    ChartHelper.updateAxisTitleWithActualValue(data, options.xAxis[0]);
    const chartOptions = defaultChartOptions.data?.data?.value
      ? merge(
          options,
          JSON.parse(defaultChartOptions.data.data.value),
          retainedChartOptions[chartKey] ?? {},
        )
      : options;

    if (settings.plotarea_only) {
      ChartHelper.setPlotAreaOnlyOptions(chartOptions);
    }

    setChartOptions(chartOptions);
  };

  // If chart data is directly set
  useEffect(() => {
    if (localChartData && defaultChartOptions.isSuccess) {
      setOptions(localChartData);
    }
  }, [defaultChartOptions.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isSuccess) {
      const response = chartDataQuery.data;
      if (response.success) {
        chartData.current = response.data;
        setOptions(response.data);
      } else {
        notificationApi.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isError) {
      notificationApi.error({
        message: component.display_name,
        description: chartDataQuery.error,
      });
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.isError]);

  /**
   * A callback function for the created chart
   *
   * @param {object} chart
   */
  const onChartLoaded = (chart) => {
    chart.shouldRenderStatsInfo = settings.has_stats_info !== false;

    if (chartCustomData) {
      ChartHelper.updateExportingMenu(
        chart,
        setIsChartOptionsOpen,
        setCurrentChart,
        chartCustomData[chartKey],
        fullScreenHandle,
      );
    }
    if (typeof setHighchartsChart === "function") {
      setHighchartsChart(chart);
    }

    redrawWhenBoosted(chart);
  };

  return (
    <div>
      {contextHolder}
      {typeof Highcharts === "object" ? (
        chartOptions ? (
          <HighchartsReact
            ref={chartRef}
            highcharts={Highcharts}
            options={chartOptions}
            callback={onChartLoaded}
            containerProps={{
              style: {
                // needed to fix highcharts react wrapper bug where chart does not display the original size after exiting full screen
                height: chartRef.current
                  ? chartRef.current.chart.chartHeight
                  : settings.height,
              },
            }}
          />
        ) : (
          <ChartLoading height={settings.height} />
        )
      ) : (
        <Empty description="Highcharts not loaded"></Empty>
      )}
    </div>
  );
}
