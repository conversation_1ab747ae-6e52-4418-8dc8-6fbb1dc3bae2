import React, { useEffect, useState } from "react";
import {
  Row,
  Col,
  Select,
  Typography,
  notification,
  Checkbox,
  Space,
} from "antd";
import { useQuery } from "@tanstack/react-query";
import Api from "../api";
import Helper from "../helper";
import { UserSettingsKeys } from "../user_settings_keys";
import ChartHelper from "../../../src/utils/charts/chart_helper";
import { QueryKeys } from "../query_keys";
import { useBoundStore } from "../../store/store";
import { ChartspaceChart } from "../components/chartspace_chart";

/**
 * Bin Patterns components
 *
 * @param {object} component
 * @param {object} chartCustomData
 * @param {string} pageKey
 * @param {object} filters
 * @param {array} waferIdOptions
 * @param {function} setWaferIdOptions
 * @param {string} chartKey
 * @param {boolean} isChartBoosted
 * @param {function} setIsChartBoosted
 * @param {function} setHasChartData
 * @param {object} fullScreenHandle
 * @param {object} prerenderData
 * @returns {JSX.Element}
 */
export default function BinPatterns({
  component,
  chartCustomData,
  pageKey,
  filters,
  waferIdOptions,
  setWaferIdOptions,
  chartKey,
  isChartBoosted,
  setIsChartBoosted,
  setHasChartData,
  fullScreenHandle,
  prerenderData,
}) {
  const [selectedChartLayout, setSelectedChartLayout] = useState(
    Helper.getUserSettings(UserSettingsKeys.more_wafer_chart_layout) ?? 2,
  );
  const [data, setData] = useState();
  const chartKeys = useBoundStore((state) => state.chartKeys);
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const reloadChartFilters = useBoundStore((state) => state.reloadChartFilters);
  const [notificationApi, notificationContextHolder] =
    notification.useNotification();
  const params = component.props.params;
  const settings = component.props.settings;

  const fetchChartData = async ({ signal }) => {
    const requestParams = Helper.filterObjectByKeys(
      filters[pageKey],
      Object.keys(params.body_params),
    );
    const url = Helper.parseUrlEndpoint(params.url_endpoint, filters[pageKey]);

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Query to fetch chart data
   */
  const dataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey),
    queryFn: fetchChartData,
  });

  useEffect(() => {
    if (dataQuery.isSuccess) {
      const response = dataQuery.data;
      if (response.success) {
        setData(response.data);
      } else {
        notificationApi.warning({
          message: component.display_name,
          description: response.message,
        });
      }
    }
  }, [dataQuery.dataUpdatedAt]);

  /**
   * Handles changes to the chart layout selection
   *
   * @param {number} value
   */
  const onChartLayoutChange = (value) => {
    Helper.setUserSettings(UserSettingsKeys.more_wafer_chart_layout, value);
    setSelectedChartLayout(value);
  };

  /**
   * Generate wafer chart patterns
   *
   * @returns {JSX.Element} element
   */
  const generatePatterns = () => {
    let element = <></>;

    if (settings.show_per_datalog) {
      let dskValues = filters[pageKey]?.src_value?.split(",");
      element = dskValues.map((dskValue) => {
        const binChartKey = `${chartKey}_${dskValue}`;
        ChartHelper.initChartCustomData(chartCustomData, binChartKey);
        return generateChart(binChartKey, {
          ...prerenderData,
          src_type: "dsk",
          src_value: dskValue,
        });
      });
    } else {
      element = data?.series_list?.map((bin) => {
        const binChartKey = `${chartKey}_${bin}`;
        ChartHelper.initChartCustomData(chartCustomData, binChartKey);
        return generateChart(binChartKey, {
          ...prerenderData,
          bin_numbers: bin.toString(),
        });
      });
    }

    return element;
  };

  /**
   * Generate wafer chart
   *
   * @param {string} chartKey
   * @param {object} prerenderData
   */
  const generateChart = (chartKey, prerenderData) => {
    return (
      <Col
        span={24 / selectedChartLayout}
        key={`${chartKey}-${selectedChartLayout}`}
      >
        {React.createElement(ChartspaceChart, {
          chartType: "wafer",
          component: component,
          filters: filters,
          pageKey: pageKey,
          waferIdOptions: waferIdOptions,
          setWaferIdOptions: setWaferIdOptions,
          chartCustomData: chartCustomData,
          chartKey: chartKey,
          isChartBoosted: isChartBoosted,
          setIsChartBoosted: setIsChartBoosted,
          setHasChartData: setHasChartData,
          fullScreenHandle: fullScreenHandle,
          prerenderData: prerenderData,
        })}
      </Col>
    );
  };

  /**
   * Reload Bin Pattern
   *
   * @param {object} e
   */
  const reloadBinPattern = (e) => {
    const binPatternChartKeys = chartKeys[pageKey][component.name];
    if (Array.isArray(binPatternChartKeys)) {
      binPatternChartKeys.forEach((chartKey) => {
        reloadChartFilters[chartKey].show_reprobe_markings = e.target.checked;
        chartComponentRefs[chartKey]?.current?.reloadChart();
      });
    }
  };

  return (
    <div className="mt-[-16px]">
      {notificationContextHolder}
      <Space>
        <Typography.Text className="flex items-center mr-1">
          Layout:
        </Typography.Text>
        <Select
          popupMatchSelectWidth
          value={selectedChartLayout}
          onChange={onChartLayoutChange}
          options={[
            { value: 2, label: "2 Charts per Row" },
            { value: 3, label: "3 Charts per Row" },
            { value: 4, label: "4 Charts per Row" },
          ]}
          placeholder="Select Layout"
        />
        {settings.has_reprobe_markings && (
          <Checkbox defaultChecked className="ml-2" onChange={reloadBinPattern}>
            Show Reprobe Markings (+)
          </Checkbox>
        )}
      </Space>
      <Row gutter={[16, 16]}>{generatePatterns()}</Row>
    </div>
  );
}
