"use client";

import { App, Empty } from "antd";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/highcharts-more";
import "highcharts/modules/no-data-to-display";
import "highcharts/modules/exporting";
import "highcharts/modules/offline-exporting";
import "highcharts/modules/accessibility";
import React, { useEffect, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { merge } from "lodash";
import Api from "../api";
import Helper from "../helper";
import { useBoundStore } from "../../store/store";
import { QueryKeys } from "../query_keys";
import { useChartDefaultOptions } from "../../hooks/useChartDefaultOptions";
import { DEFAULT_PLOTLINES } from "./constants";
import ChartHelper from "./chart_helper";
import { setPlotBands } from "./chart_common";
import ChartLoading from "./loading";

/**
 * Box Whisker with Line chart
 *
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @param {function} setHasChartData
 * @param {object} fullScreenHandle
 * @param {object} prerenderData
 * @param {React.Ref} ref
 * @returns {JSX.Element}
 */
export default React.forwardRef(function BoxPlotWithLine(
  {
    chartRef,
    component,
    filters,
    pageKey,
    chartKey,
    chartCustomData,
    setHasChartData,
    fullScreenHandle,
    prerenderData = {},
  },
  ref,
) {
  const [chartOptions, setChartOptions] = useState();
  const [topFailingTestData, setTopFailingTestData] = useState();
  const [shouldFetchChartData, setShouldFetchChartData] = useState(false);
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const urlParams = useBoundStore((state) => state.urlParams);
  const cacheData = useBoundStore((state) => state.cacheData);
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const setCurrentChart = useBoundStore((state) => state.setCurrentChart);
  const retainedChartOptions = useBoundStore(
    (state) => state.retainedChartOptions,
  );
  const { message, notification } = App.useApp();
  const queryClient = useQueryClient();
  const params = component.props.params;
  const settings = component.props.settings;
  const chartType = component.name;

  const options = merge(ChartHelper.getChartDefaultSettings(), {
    chart: {
      type: "boxplot",
      zoomType: "xy",
      events: {
        render: (event) => {
          if (chartCustomData[chartKey].shouldUpdateXAxisHeight !== false) {
            chartCustomData[chartKey].shouldUpdateXAxisHeight = false;
            updateXAxisHeight(event.target);
          }
        },
      },
    },
    title: {
      text: settings.show_title ? settings.title : "",
      align: "left",
    },
    subtitle: {},
    xAxis: [
      {
        title: {
          text: ChartHelper.generateChartTitle(
            settings?.x?.title ?? "",
            merge({}, filters[pageKey], prerenderData),
          ),
        },
        tickInterval: 1,
        labels: {
          rotation: -75,
        },
        events: {
          afterSetExtremes: (event) => {
            const chart = event.target.chart;
            ChartHelper.highlightCategories(
              chart,
              chart.series[0],
              chartCustomData[chartKey].categories,
              chartCustomData[chartKey].highlightCategories,
            );
          },
        },
      },
    ],
    yAxis: [
      {
        title: {
          text: settings.y.title,
        },
      },
      {
        title: {
          text: settings?.y2?.title ?? "",
        },
        opposite: true,
        gridLineWidth: 0,
      },
    ],
    series: [
      {
        name: settings.boxplot.title,
        yAxis: 0,
        data: [],
        tooltip: {
          headerFormat: "",
        },
      },
      {
        type: "line",
        name: settings?.line?.title ?? "",
        yAxis: 1,
        zIndex: 10,
        tooltip: {
          headerFormat: "",
        },
        data: [],
      },
    ],
    yhDefinedConfig: {
      name: Helper.titlelize(chartType),
      chartType: chartType,
      chartKey: chartKey,
      resultsAxis: "y",
      logScaleAxis: "y",
      hasStatsInfo: true,
      resultsAxisRange: "dataAndLimits",
      default: settings,
    },
  });

  useEffect(() => {
    if (typeof Highcharts !== "object") {
      message.warning("Highcharts not loaded.", 10);
    }
    chartComponentRefs[chartKey] = ref;

    return () => {
      queryClient.removeQueries({
        queryKey: QueryKeys.chart(pageKey, chartKey),
      });
    };
  }, []);

  React.useImperativeHandle(ref, () => ({
    reloadChart: () => {
      chartDataQuery.refetch();
    },
  }));

  useEffect(() => {
    if (component.props.params.body_params.test_number !== undefined) {
      setTopFailingTestData({
        test_number: component.props.params.body_params.test_number,
      });
    } else {
      Helper.getLotTopFailingTestData(
        urlParams[pageKey].src_type,
        urlParams[pageKey].src_value,
        urlParams[pageKey].mfg_process,
        setTopFailingTestData,
        message.warning,
        message.error,
        cacheData,
      );
    }
  }, []);

  useEffect(() => {
    if (topFailingTestData !== undefined) {
      setShouldFetchChartData(true);
    }
  }, [topFailingTestData]);

  /**
   * Update chart height for label rotation
   *
   * @param {object} chart
   */
  const updateXAxisHeight = (chart) => {
    const xAxis = chart.xAxis[0];
    const labelHeights = Object.values(xAxis.ticks).map((tick) => {
      return tick.label.getBBox().height;
    });
    const maxLabelHeight = Helper.getArrayMax(labelHeights);
    const defaultSpacingBottom = 20;

    options.chart.spacingBottom = maxLabelHeight + defaultSpacingBottom;
    options.chart.height = chart.chartHeight + maxLabelHeight;
    chart.renderTo.style.height = `${options.chart.height}px`;
    chart.setSize(chart.chartWidth, options.chart.height, true);
  };

  /**
   * Fetch chart data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchChartData = async ({ signal }) => {
    chartRef.current?.chart?.showLoading();

    filters[pageKey].tNum = topFailingTestData.test_number;
    const allFilters = { ...filters[pageKey], ...prerenderData };
    const url = Helper.parseUrlEndpoint(params.url_endpoint, allFilters);
    const requestParams = Helper.filterObjectByKeys(
      allFilters,
      Object.keys(params.body_params),
    );

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Fetch the default chart options of this chart type
   */
  const defaultChartOptions = useChartDefaultOptions(chartType);

  /**
   * Query to fetch chart data
   */
  const chartDataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey),
    queryFn: fetchChartData,
    enabled: shouldFetchChartData && defaultChartOptions.isSuccess,
  });

  useEffect(() => {
    if (chartDataQuery.isSuccess) {
      const response = chartDataQuery.data;
      if (response.success) {
        setHasChartData(
          Array.isArray(response.data.boxplot_data) &&
            response.data.boxplot_data.length > 0,
        );
        if (Object.keys(response.data).length > 0) {
          options.subtitle.text = `Test # ${response.data.actual_test_number} :: ${response.data.test_name} (${response.data.test_unit || response.data.test_unit !== "" ? response.data.test_unit : "-"})<br/>Test Limits [${response.data.lo_lim ?? "-"}, ${response.data.hi_lim ?? "-"}]`;
        }
        const categories = response.data.x_categories;
        const categoryColors = getCategoryColors(categories);
        options.xAxis[0].labels.formatter = function () {
          const category = categories[this.value];
          return `<span style="color: ${categoryColors[category]}">${category}</span>`;
        };
        chartCustomData[chartKey].categories = response.data.x_categories;
        chartCustomData[chartKey].highlightCategories =
          response.data?.highlight_x_categories ?? [];

        const boxplotStats = Array.isArray(response.data.boxplot_data)
          ? response.data.boxplot_data.map((data) => {
              const statsData = { mean: data[5], stdev: data[6] };
              // remove mean and stdev from boxplot data
              data.splice(5, 2);
              return statsData;
            })
          : [];
        options.series[0].tooltip.pointFormatter = function () {
          return `<strong>${categories[this.index]}</strong><br>
            Max Result: ${this.high}<br>
            Upper Quartile: ${this.q3}<br>
            Median: ${this.median}<br>
            Lower Quartile: ${this.q1}<br>
            Min Result: ${this.low}<br>
            Mean: ${boxplotStats[this.index].mean}<br>
            Standard Deviation: ${boxplotStats[this.index].stdev}`;
        };
        options.series[1].tooltip.pointFormatter = function () {
          return `<strong>${categories[this.index]}</strong><br>
            ${settings?.y2?.title}: ${Helper.numberFormat(this.y, 3)}%`;
        };

        options.series[0].data = response.data.boxplot_data;
        options.series[1].data = response.data.line_data;

        options.yAxis[0].min = Math.min(
          response.data.min_result,
          response.data.lo_limit ?? response.data.min_result,
        );
        options.yAxis[0].max = Math.max(
          response.data.max_result,
          response.data.hi_lim ?? response.data.max_result,
        );
        options.yAxis[0].plotLines = [
          ...ChartHelper.getPlotLines(response.data, DEFAULT_PLOTLINES),
          ...ChartHelper.getSigmaLines(response.data),
        ];
        // Append the test data to the user options
        options.testData = ChartHelper.setTestDataToUserOptions(response.data);
        ChartHelper.updateAxisTitleWithActualValue(
          response.data,
          options.yAxis[0],
        );
        setPlotBands(response.data.plot_band_data, options);
        const chartOptions = defaultChartOptions.data?.data?.value
          ? merge(
              options,
              JSON.parse(defaultChartOptions.data.data.value),
              retainedChartOptions[chartKey] ?? {},
            )
          : options;
        setChartOptions(chartOptions);
      } else {
        notification.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isError) {
      notification.error({
        message: component.display_name,
        description: chartDataQuery.error,
      });
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.isError]);

  /**
   * Get colors by category
   *
   * @param {array} categories
   * @returns {object} categoryColors
   */
  const getCategoryColors = (categories) => {
    const uniqueCategories = Helper.arrayUnique(categories);
    const categoryColors = uniqueCategories.reduce((obj, item, index) => {
      obj[item] = Highcharts.getOptions().colors[index];
      return obj;
    }, {});

    return categoryColors;
  };

  /**
   * A callback function for the created chart
   *
   * @param {object} chart
   */
  const onChartLoaded = (chart) => {
    ChartHelper.updateExportingMenu(
      chart,
      setIsChartOptionsOpen,
      setCurrentChart,
      chartCustomData[chartKey],
      fullScreenHandle,
    );
  };

  return (
    <div>
      {typeof Highcharts === "object" ? (
        chartOptions ? (
          <HighchartsReact
            ref={chartRef}
            highcharts={Highcharts}
            options={chartOptions}
            callback={onChartLoaded}
            containerProps={{
              style: {
                // needed to fix highcharts react wrapper bug where chart does not display the original size after exiting full screen
                height: chartRef.current
                  ? chartRef.current.chart.chartHeight
                  : settings.height,
              },
            }}
          />
        ) : (
          <ChartLoading height={settings.height} />
        )
      ) : (
        <Empty description="Highcharts not loaded"></Empty>
      )}
    </div>
  );
});
