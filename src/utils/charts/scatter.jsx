"use client";

import { App, Empty, notification } from "antd";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/highcharts-more";
import "highcharts/modules/no-data-to-display";
import "highcharts/modules/exporting";
import "highcharts/modules/offline-exporting";
import "highcharts/modules/accessibility";
import "highcharts/modules/boost";
import React, { useEffect, useRef, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { concat, merge } from "lodash";
import Api from "../api";
import Helper from "../helper";
import { useBoundStore } from "../../store/store";
import { QueryKeys } from "../query_keys";
import { useChartDefaultOptions } from "../../hooks/useChartDefaultOptions";
import { DEFAULT_PLOTLINES } from "./constants";
import ChartHelper from "./chart_helper";
import ChartLoading from "./loading";
import { redrawWhenBoosted, setAxisMinMax } from "./chart_common";

/**
 * Scatter chart
 *
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @param {object} chartFilters
 * @param {function} testStatsInfoHandler Handler of the setting the test stats info
 * @param {function} setIsChartBoosted
 * @param {function} setHasChartData
 * @param {object} fullScreenHandle
 * @param {object} prerenderData
 * @param {React.Ref} ref
 * @returns {JSX.Element}
 */
export default React.forwardRef(function Scatter(
  {
    chartRef,
    component,
    filters,
    pageKey,
    chartKey,
    chartCustomData,
    chartFilters = {},
    testStatsInfoHandler,
    setIsChartBoosted,
    setHasChartData,
    fullScreenHandle,
    prerenderData = {},
  },
  ref,
) {
  const [chartOptions, setChartOptions] = useState();
  const [topFailingTestData, setTopFailingTestData] = useState();
  const [shouldFetchChartData, setShouldFetchChartData] = useState(false);
  const urlParams = useBoundStore((state) => state.urlParams);
  const cacheData = useBoundStore((state) => state.cacheData);
  const setActiveTestNumber = useBoundStore(
    (state) => state.setActiveTestNumber,
  );
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const setCurrentChart = useBoundStore((state) => state.setCurrentChart);
  const reloadChartFilters = useBoundStore((state) => state.reloadChartFilters);
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const retainedChartOptions = useBoundStore(
    (state) => state.retainedChartOptions,
  );
  const pageMeta = useBoundStore((state) => state.pageMeta);
  const chartData = useRef({});
  const { message } = App.useApp();
  const [notificationApi, contextHolder] = notification.useNotification();
  const queryClient = useQueryClient();
  const params = component.props.params;
  const settings = component.props.settings;
  const chartType = component.name;

  const options = merge(ChartHelper.getChartDefaultSettings(true, true), {
    chart: {
      zoomType: "xy",
      events: {
        fullscreenOpen: () => {
          chartCustomData[chartKey].shouldSetStatsInfoPosition = true;
          chartCustomData[chartKey].isFullScreen = true;
        },
        fullscreenClose: () => {
          chartCustomData[chartKey].shouldSetStatsInfoPosition = true;
          chartCustomData[chartKey].isFullScreen = false;
        },
        render: (event) => {
          const chart = event.target;
          if (typeof setIsChartBoosted === "function") {
            setIsChartBoosted(chart.boosted);
          }
          if (
            chartCustomData[chartKey].shouldSetStatsInfoPosition ||
            chart.shouldRenderStatsInfo
          ) {
            chartCustomData[chartKey].shouldSetStatsInfoPosition = false;
            chart.shouldRenderStatsInfo = false;

            ChartHelper.renderStatsInfoToChart(
              chart,
              ChartHelper.getChartDefaultStats(),
              chartData.current,
              options,
              chartCustomData[chartKey],
            );
          }
          if (chartCustomData[chartKey].shouldShowDatalogLines) {
            chartCustomData[chartKey].shouldShowDatalogLines = false;
            options.xAxis[0].plotLines = ChartHelper.getPlotLines(
              chartData.current,
              ChartHelper.filterPlotLines(
                concat(DEFAULT_PLOTLINES, ["dlog_info"]),
                chartFilters,
              ),
              true,
              chart.plotHeight - 16,
            );
            chart.xAxis[0].update({
              plotLines: options.xAxis[0].plotLines,
            });
          }
        },
      },
    },
    title: {
      text: chartFilters.title ?? settings.title,
    },
    subtitle: {},
    boost: {
      useGPUTranslations: true,
      usePreAllocated: true,
    },
    xAxis: [
      {
        title: {
          text: settings.x.title,
        },
        tickWidth: 0,
      },
    ],
    yAxis: [
      {
        minPadding: 0,
        maxPadding: 0,
        title: {
          text: settings.y.title,
        },
      },
    ],
    exporting: {
      chartOptions: {
        chart: {
          events: {
            load: () => {
              chartCustomData[chartKey].shouldSetStatsInfoPosition = true;
              chartCustomData[chartKey].shouldRemoveStatsInfoElement = false;
            },
          },
        },
      },
    },
    series: [],
    yhDefinedConfig: {
      name: Helper.titlelize(chartType),
      chartType: chartType,
      chartKey: chartKey,
      resultsAxis: "y",
      logScaleAxis: "y",
      hasStatsInfo: true,
      default: settings,
    },
  });

  React.useImperativeHandle(ref, () => ({
    reloadChart: () => {
      chartDataQuery.refetch();
    },
  }));

  useEffect(() => {
    if (typeof Highcharts !== "object") {
      message.warning("Highcharts not loaded.", 10);
    }
    chartComponentRefs[chartKey] = ref;

    return () => {
      queryClient.removeQueries({
        queryKey: QueryKeys.chart(pageKey, chartKey),
      });
    };
  }, []);

  useEffect(() => {
    if (component.props.params.body_params.test_number !== undefined) {
      setTopFailingTestData({
        test_number: component.props.params.body_params.test_number,
      });
    } else {
      Helper.getLotTopFailingTestData(
        urlParams[pageKey].src_type,
        urlParams[pageKey].src_value,
        urlParams[pageKey].mfg_process,
        setTopFailingTestData,
        message.warning,
        message.error,
        cacheData,
      );
      setActiveTestNumber("topFailingTest");
    }
  }, []);

  useEffect(() => {
    if (topFailingTestData !== undefined) {
      setShouldFetchChartData(true);
    }
  }, [topFailingTestData]);

  /**
   * Fetch chart data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchChartData = async ({ signal }) => {
    chartRef.current?.chart.showLoading();

    filters[pageKey].tNum = topFailingTestData.test_number;
    let allFilters = {
      ...filters[pageKey],
      ...chartFilters,
      ...prerenderData,
      ...reloadChartFilters[chartKey],
    };
    const url = Helper.parseUrlEndpoint(params.url_endpoint, allFilters);
    const requestParams = Helper.filterObjectByKeys(
      allFilters,
      Object.keys(params.body_params),
    );

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Fetch the default chart options of this chart type
   */
  const defaultChartOptions = useChartDefaultOptions(chartType);

  /**
   * Query to fetch chart data
   */
  const chartDataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey),
    queryFn: fetchChartData,
    enabled: shouldFetchChartData && defaultChartOptions.isSuccess,
  });

  useEffect(() => {
    if (chartDataQuery.isSuccess) {
      const response = chartDataQuery.data;
      if (response.success) {
        setHasChartData(
          Array.isArray(response.data.data) && response.data.data.length > 0,
        );
        chartData.current = response.data;
        if (Object.keys(response.data).length > 0) {
          options.subtitle.text = `Test # ${response.data.actual_test_number} :: ${response.data.test_name} (${response.data.test_unit || response.data.test_unit !== "" ? response.data.test_unit : "-"})<br/>Test Limits [${response.data.lo_lim ?? "-"}, ${response.data.hi_lim ?? "-"}]`;
        }
        const categories = response.data?.x_categories ?? [];
        options.xAxis[0].labels.formatter = function () {
          return categories[this.value];
        };

        response.data.data?.forEach((data, i) => {
          options.series.push({
            type: "scatter",
            name: response.data.chart_legend[i] ?? "",
            data: data,
            marker: {
              enabled: true,
              symbol: "circle",
              radius: 3,
            },
            tooltip: {
              followPointer: false,
            },
            boostThreshold: 2000,
          });

          options.series[i].tooltip.pointFormatter = function () {
            return (
              `${options.xAxis[0].title.text}: ${categories[this.x]}<br/>` +
              `${this.y} ${response.data.test_unit ?? ""}<br/>` +
              `Bin #: ${response.data.sbins[0][this.index]}<br/>` +
              `${pageMeta.analysis_type !== "single" && response.data?.dlog_info ? `Datalog: ${getDlogFilename(this.index, response.data.dlog_info)}<br/>` : ""}`
            );
          };
        });

        chartRef.current?.chart?.xAxis[0].removePlotLine("dlog_info");
        if (reloadChartFilters[chartKey].show_datalog_lines === true) {
          const datalogPlotLines = ChartHelper.getPlotLines(
            chartData.current,
            ["dlog_info"],
            true,
            chartRef.current?.chart?.plotHeight - 16,
          );
          datalogPlotLines.forEach((plotLine) => {
            chartRef.current?.chart?.xAxis[0].addPlotLine(plotLine);
          });
        }

        // if (chartFilters.show_limits === true) {
        //   options.yAxis.plotLines = ChartHelper.drawLimitLines(response.data);
        // }
        setAxisMinMax(options.yAxis[0], response.data);
        options.yAxis[0].plotLines = [
          ...ChartHelper.getPlotLines(
            response.data,
            ChartHelper.filterPlotLines(DEFAULT_PLOTLINES, chartFilters),
          ),
          ...ChartHelper.getSigmaLines(response.data),
        ];
        // Append the test data to the user options
        options.testData = ChartHelper.setTestDataToUserOptions(response.data);
        ChartHelper.updateAxisTitleWithActualValue(
          response.data,
          options.yAxis[0],
        );

        const chartOptions = defaultChartOptions.data?.data?.value
          ? merge(
              options,
              JSON.parse(defaultChartOptions.data.data.value),
              retainedChartOptions[chartKey] ?? {},
            )
          : options;
        setChartOptions(chartOptions);
        if (topFailingTestData) {
          testStatsInfoHandler(response.data, topFailingTestData.test_number);
          setActiveTestNumber(topFailingTestData.test_number);
        }
      } else {
        notificationApi.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isError) {
      notificationApi.error({
        message: component.display_name,
        description: chartDataQuery.error,
      });
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.isError]);

  /**
   * A callback function for the created chart
   *
   * @param {object} chart
   */
  const onChartLoaded = (chart) => {
    chart.shouldRenderStatsInfo = settings.has_stats_info !== false;

    ChartHelper.updateExportingMenu(
      chart,
      setIsChartOptionsOpen,
      setCurrentChart,
      chartCustomData[chartKey],
      fullScreenHandle,
    );

    redrawWhenBoosted(chart);
  };

  /**
   * Returns the filename corresponding to a given point index based on the `dlogInfo` mapping.
   *
   * @param {number} pointIndex
   * @param {array} dlogInfo
   * @returns {string} dlogName
   */
  const getDlogFilename = (pointIndex, dlogInfo) => {
    let dlogName = "";
    for (const [maxIndex, filename] of dlogInfo) {
      if (pointIndex <= maxIndex) {
        dlogName = filename;
        break;
      }
    }
    return dlogName;
  };

  return (
    <div>
      {contextHolder}
      {typeof Highcharts === "object" ? (
        chartOptions ? (
          <HighchartsReact
            ref={chartRef}
            highcharts={Highcharts}
            options={chartOptions}
            callback={onChartLoaded}
            containerProps={{
              style: {
                // needed to fix highcharts react wrapper bug where chart does not display the original size after exiting full screen
                height: chartRef.current?.chart?.chartHeight ?? settings.height,
              },
            }}
          />
        ) : (
          <ChartLoading height={settings.height} />
        )
      ) : (
        <Empty description="Highcharts not loaded"></Empty>
      )}
    </div>
  );
});
