"use client";

import { App, Empty, notification } from "antd";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/modules/no-data-to-display";
import "highcharts/modules/exporting";
import "highcharts/modules/offline-exporting";
import "highcharts/modules/accessibility";
import "highcharts/modules/boost";
import React, { useEffect, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { merge } from "lodash";
import Api from "../api";
import Helper from "../helper";
import { useBoundStore } from "../../store/store";
import { QueryKeys } from "../query_keys";
import { useChartDefaultOptions } from "../../hooks/useChartDefaultOptions";
import ChartHelper from "./chart_helper";
import ChartLoading from "./loading";
import { redrawWhenBoosted } from "./chart_common";

/**
 * Stacked column chart
 *
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @param {function} setIsChartBoosted
 * @param {function} setHasChartData
 * @param {object} fullScreenHandle
 * @param {object} prerenderData
 * @param {React.Ref} ref
 * @returns {JSX.Element}
 */
export default React.forwardRef(function StackedColumn(
  {
    chartRef,
    component,
    filters,
    pageKey,
    chartKey,
    chartCustomData,
    setIsChartBoosted,
    setHasChartData,
    fullScreenHandle,
    prerenderData = {},
  },
  ref,
) {
  const [chartOptions, setChartOptions] = useState();
  const reloadChartFilters = useBoundStore((state) => state.reloadChartFilters);
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const setCurrentChart = useBoundStore((state) => state.setCurrentChart);
  const retainedChartOptions = useBoundStore(
    (state) => state.retainedChartOptions,
  );
  const { message } = App.useApp();
  const queryClient = useQueryClient();
  const params = component.props.params;
  const settings = component.props.settings;
  const chartType = component.name;

  const options = merge(ChartHelper.getChartDefaultSettings(), {
    chart: {
      type: "column",
      width: settings.width ? settings.width : null,
      height: settings.height ? settings.height : "auto",
      zoomType: "xy",
      events: {
        render: (event) => {
          const chart = event.target;
          if (typeof setIsChartBoosted === "function") {
            setIsChartBoosted(chart.boosted);
          }
        },
      },
    },
    title: {
      text: ChartHelper.generateChartTitle(
        settings.title ?? "",
        merge({}, filters[pageKey], prerenderData),
      ),
    },
    xAxis: [
      {
        categories: [],
        title: {
          text: ChartHelper.generateChartTitle(
            settings?.x?.title ?? "",
            merge({}, filters[pageKey], prerenderData),
          ),
        },
      },
    ],
    yAxis: [
      {
        min: 0,
        title: {
          text: ChartHelper.generateChartTitle(
            settings.y.title,
            merge({}, filters[pageKey], prerenderData),
          ),
        },
        stackLabels: {
          enabled: false,
        },
        labels: {
          format: "{value}%",
        },
        reversedStacks: false,
      },
    ],
    plotOptions: {
      series: {
        custom: {
          settings: settings,
          component: component,
        },
        dataLabels: {
          enabled: prerenderData.show_bin_count ?? false,
          formatter: function () {
            const countData =
              this.series.options.custom.count?.[this.point.index] ?? null;
            return countData !== null ? Helper.numberFormat(countData, 2) : "";
          },
        },
      },
      column: {
        stacking: "normal",
      },
    },
    series: [],
    legend: {
      events: {
        itemClick(e) {
          let chart = this.chart;

          // Handles visibility of series with group legend
          chart.series.forEach((s) => {
            if (s.options.custom?.groupLegend) {
              const sameGroup =
                e.legendItem.name === s.options.custom?.groupLegend ||
                e.legendItem.name === s.name;

              if (sameGroup && e.legendItem.visible === s.visible) {
                s.setVisible(!s.visible, false);
              }
            }
          });

          // Handles visibility of selected series
          e.legendItem.setVisible(!e.legendItem.visible, false);
          chart.redraw();

          return false;
        },
      },
    },
    yhDefinedConfig: {
      name: Helper.titlelize(chartType),
      chartType: chartType,
      chartKey: chartKey,
      default: settings,
    },
  });

  useEffect(() => {
    if (typeof Highcharts !== "object") {
      message.warning("Highcharts not loaded.", 10);
    }
    chartComponentRefs[chartKey] = ref;

    return () => {
      queryClient.removeQueries({
        queryKey: QueryKeys.chart(pageKey, chartKey),
      });
    };
  }, []);

  React.useImperativeHandle(ref, () => ({
    reloadChart: () => {
      if (reloadChartFilters[chartKey].chart_bin_type) {
        filters[pageKey].bin_type = reloadChartFilters[chartKey].chart_bin_type;
        delete reloadChartFilters[chartKey].chart_bin_type;
        updateChartTitle();
      }
      chartDataQuery.refetch();
    },
  }));

  /**
   * Update chart title based on bin type
   */
  const updateChartTitle = () => {
    // TODO: Use data returned in API response when getting chart data in updating chart title
    if (filters[pageKey].bin_type) {
      settings.title =
        filters[pageKey].bin_type === "soft"
          ? settings.title.replace("Hardware", "Software")
          : settings.title.replace("Software", "Hardware");
      options.title.text = settings.title;
    }
  };

  /**
   * Fetch chart data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchChartData = async ({ signal }) => {
    chartRef.current?.chart?.hideNoData();
    chartRef.current?.chart?.showLoading();
    prerenderData.use_pass_bins
      ? (prerenderData.pass_fail_status = "Pass")
      : (prerenderData.pass_fail_status = "Fail");
    const allFilters = { ...filters[pageKey], ...prerenderData };
    const url = Helper.parseUrlEndpoint(params.url_endpoint, allFilters);
    const requestParams = Helper.filterObjectByKeys(
      allFilters,
      Object.keys(params.body_params),
    );

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Fetch the default chart options of this chart type
   */
  const defaultChartOptions = useChartDefaultOptions(chartType);

  /**
   * Query to fetch chart data
   */
  const chartDataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey),
    queryFn: fetchChartData,
    enabled: defaultChartOptions.isSuccess,
  });

  useEffect(() => {
    if (chartDataQuery.isSuccess) {
      const response = chartDataQuery.data;
      if (response.success) {
        setHasChartData(
          response.data.y_data && Object.keys(response.data.y_data).length > 0,
        );
        options.xAxis[0].categories = response.data.x_categories;
        options.series = generateStackedSeries(response.data ?? {});
        options.tooltip = generateTooltip(response.data ?? {});
        if (response.data.group_chart_legend) {
          generateGroupStackLabels(response.data.series_colors);
        }
        const chartOptions = defaultChartOptions.data?.data?.value
          ? merge(
              options,
              JSON.parse(defaultChartOptions.data.data.value),
              retainedChartOptions[chartKey] ?? {},
            )
          : options;
        setChartOptions(chartOptions);
      } else {
        notification.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isError) {
      notification.error({
        message: component.display_name,
        description: chartDataQuery.error,
      });
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.isError]);

  /**
   * Generate stacked column(s) series
   *
   * @param {object} data
   * @returns {array} stackedSeries
   */
  const generateStackedSeries = (data) => {
    const yData = Array.isArray(data.y_data)
      ? data.y_data
      : [data.y_data ?? {}];
    const countData = data.fails_count ?? data.count;
    const count = Array.isArray(countData) ? countData : [countData ?? {}];
    const allPercentage = data?.all_percentage ?? [];
    let stackedSeries = [];

    yData.forEach((dataItem, index) => {
      Object.keys(dataItem).forEach((seriesKey, seriesIndex) => {
        const groupId = `group${index + 1}`;
        const groupLegend = data?.group_chart_legend?.[groupId];

        stackedSeries.push({
          name: `${data?.bar_labels?.[seriesIndex]}`,
          showInLegend: data.group_chart_legend ? false : true,
          stack: groupId,
          color: data.series_colors?.[seriesKey],
          data: dataItem[seriesKey],
          custom: {
            seriesKey,
            groupLegend,
            passFailStatus: prerenderData.pass_fail_status,
            count: count[index]?.[seriesKey],
            allPct: allPercentage?.[index]?.[seriesKey],
          },
        });
      });
    });

    if (data.group_chart_legend) {
      // Plot common bar legend
      data.bar_labels?.forEach((bar) => {
        stackedSeries.push({
          name: bar,
          showInLegend: true,
          color: data.series_colors?.[bar],
          data: [],
        });
      });

      // Plot group legend
      Object.keys(data.group_chart_legend)?.forEach((groupKey) => {
        stackedSeries.push({
          name: data.group_chart_legend[groupKey],
          showInLegend: true,
          color: data.series_colors?.[groupKey],
          data: [],
        });
      });
    }

    return stackedSeries;
  };

  /**
   * Generate tooltip
   *
   * @param {object} data
   * @returns {object}
   */
  const generateTooltip = (data) => {
    return {
      formatter() {
        const {
          seriesKey,
          groupLegend,
          passFailStatus,
          count = [],
          allPct = [],
        } = this.series.options.custom;

        const header = `<b>${ChartHelper.generateChartTitle(
          settings?.tooltip?.title ?? "",
          merge({}, filters[pageKey], data, {
            series: seriesKey,
            x_categories: this.key,
            group_chart_legend: groupLegend,
          }),
        )}</b><br/>`;

        const body = groupLegend
          ? `${this.series.name}<br/>
           % ${passFailStatus}: <b>${Helper.numberFormat(this.y, 2)}%</b><br/>
           % of all ${passFailStatus}: <b>${Helper.numberFormat(allPct[this.point.index], 2)}%</b><br/>`
          : `${this.series.name} ${passFailStatus} %: <b>${Helper.numberFormat(this.y, 2)} %</b><br/>`;

        const cnt = count[this.point.index];
        const countStr =
          cnt !== undefined
            ? `${passFailStatus} Count: <b>${Helper.numberFormat(cnt)}</b>`
            : "";

        return header + body + countStr;
      },
    };
  };

  /**
   * A callback function for the created chart
   *
   * @param {object} chart
   */
  const onChartLoaded = (chart) => {
    ChartHelper.updateExportingMenu(
      chart,
      setIsChartOptionsOpen,
      setCurrentChart,
      chartCustomData[chartKey],
      fullScreenHandle,
    );

    redrawWhenBoosted(chart);
  };

  /**
   * Generate group stack labels
   *
   * @param {object} seriesColors
   */
  const generateGroupStackLabels = (seriesColors) => {
    options.yAxis[0].stackLabels = {
      enabled: true,
      useHTML: true,
      formatter: function () {
        return `
                <div style="width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">
                  <div 
                    style="
                      width: 10px; 
                      height: 10px; 
                      border-radius: 50%; 
                      border: 1px solid transparent; 
                      background-color: ${seriesColors[this.stack]};
                    ">
                  </div>
                </div>
              `;
      },
    };
  };

  return (
    <div>
      {typeof Highcharts === "object" ? (
        chartOptions ? (
          <HighchartsReact
            ref={chartRef}
            highcharts={Highcharts}
            options={chartOptions}
            callback={onChartLoaded}
            containerProps={{
              style: {
                // needed to fix highcharts react wrapper bug where chart does not display the original size after exiting full screen
                height: chartRef.current
                  ? chartRef.current.chart.chartHeight
                  : settings.height,
              },
            }}
          />
        ) : (
          <ChartLoading height={settings.height} />
        )
      ) : (
        <Empty description="Highcharts not loaded"></Empty>
      )}
    </div>
  );
});
