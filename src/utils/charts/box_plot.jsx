"use client";

import { App, Empty, notification } from "antd";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/highcharts-more";
import "highcharts/modules/no-data-to-display";
import "highcharts/modules/exporting";
import "highcharts/modules/offline-exporting";
import "highcharts/modules/accessibility";
import React, { useEffect, useRef, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { concat, merge } from "lodash";
import Api from "../api";
import Helper from "../helper";
import { useBoundStore } from "../../store/store";
import { QueryKeys } from "../query_keys";
import { useChartDefaultOptions } from "../../hooks/useChartDefaultOptions";
import { useLocalBoxPlotData } from "../../hooks/useLocalBoxPlotData";
import { DEFAULT_PLOTLINES } from "./constants";
import ChartHelper from "./chart_helper";
import {
  getBoxPlotSeries,
  getScatterSeries,
  getResultsSubtitle,
  setAxisMinMax,
  setPlotBands,
} from "./chart_common";
import ChartLoading from "./loading";

/**
 * Box Plot chart
 *
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @param {object} chartFilters
 * @param {function} setHasChartData
 * @param {function} setHighchartsChart
 * @param {object} fullScreenHandle
 * @param {boolean} hasTopFailingTests
 * @param {object} localChartData
 * @param {boolean} boostMode
 * @param {object} prerenderData
 * @param {React.Ref} ref
 * @returns {JSX.Element}
 */
export default React.forwardRef(function BoxPlot(
  {
    chartRef,
    component,
    filters,
    pageKey,
    chartKey,
    chartCustomData,
    chartFilters = {},
    setHasChartData,
    setHighchartsChart,
    fullScreenHandle,
    hasTopFailingTests: hasTopFailingTestsProp = true,
    localChartData,
    boostMode,
    prerenderData = {},
  },
  ref,
) {
  // Define custom symbol for horizontal line markers (used for per-category limits)
  if (
    typeof Highcharts?.SVGRenderer?.prototype?.symbols?.hline !== "function"
  ) {
    Highcharts.SVGRenderer.prototype.symbols.hline = function (x, y, w, h) {
      return ["M", x, y + h / 2, "L", x + w, y + h / 2];
    };
  }
  const [chartOptions, setChartOptions] = useState();
  // Allow external components (e.g., grids) to inject local chart data
  const [externalLocalData, setExternalLocalData] = useState();
  const [topFailingTestData, setTopFailingTestData] = useState();
  const [shouldFetchChartData, setShouldFetchChartData] = useState(false);
  const urlParams = useBoundStore((state) => state.urlParams);
  const cacheData = useBoundStore((state) => state.cacheData);
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const setCurrentChart = useBoundStore((state) => state.setCurrentChart);
  const retainedChartOptions = useBoundStore(
    (state) => state.retainedChartOptions,
  );
  const chartData = useRef({});
  const { message } = App.useApp();
  const [notificationApi, contextHolder] = notification.useNotification();
  const queryClient = useQueryClient();
  const params = component.props.params;
  const settings = component.props.settings;
  const chartType = component.name;
  // Generic flag from blueprint to indicate that this chart should not perform
  // its own data fetching and will rely on locally injected data instead.
  const isLocalDataOnly = component?.props?.settings?.local_data_only === true;
  const popPendingLocalChartData = useBoundStore(
    (state) => state.popPendingLocalChartData,
  );
  const hasTopFailingTests =
    settings.has_top_failing_tests ?? hasTopFailingTestsProp;
  // NOTE: Highcharts currently does not support boost mode for box plot chart
  const boost = settings.boost_mode ?? boostMode;

  const options = merge(ChartHelper.getChartDefaultSettings(true, boost), {
    chart: {
      type: "boxplot",
      zoomType: "xy",
      events: {
        fullscreenOpen: () => {
          chartCustomData[chartKey].shouldSetStatsInfoPosition = true;
          chartCustomData[chartKey].isFullScreen = true;
        },
        fullscreenClose: () => {
          chartCustomData[chartKey].shouldSetStatsInfoPosition = true;
          chartCustomData[chartKey].isFullScreen = false;
        },
        render: ({ target: chart }) => {
          // Render stats info when initially enabled OR when we only need to reposition it
          const needsRender =
            chart.shouldRenderStatsInfo ||
            chartCustomData?.[chartKey]?.shouldSetStatsInfoPosition;
          if (needsRender) {
            chartCustomData[chartKey].shouldSetStatsInfoPosition = false;
            chart.shouldRenderStatsInfo = false;

            ChartHelper.renderStatsInfoToChart(
              chart,
              ChartHelper.getChartDefaultStats(),
              chartData.current,
              options,
              chartCustomData[chartKey],
            );
          }
        },
      },
    },
    plotOptions: {
      series: {
        stacking: settings?.seriesStacking ?? "overlap",
      },
    },
    title: {
      text: settings.show_title ? (chartFilters.title ?? settings.title) : "",
      align: "left",
    },
    subtitle: {},
    xAxis: [
      {
        title: {
          text: ChartHelper.generateChartTitle(
            settings?.x?.title ?? "",
            merge({}, filters[pageKey], prerenderData),
          ),
        },
        events: {
          afterSetExtremes: (event) => {
            const chart = event.target.chart;
            ChartHelper.highlightCategories(
              chart,
              chart.series[0],
              chartCustomData[chartKey].categories,
              chartCustomData[chartKey].highlightCategories,
            );
          },
        },
      },
    ],
    yAxis: [
      {
        title: {
          text: settings.y.title,
        },
      },
    ],
    exporting: {
      chartOptions: {
        chart: {
          events: {
            load: () => {
              chartCustomData[chartKey].shouldSetStatsInfoPosition = true;
              chartCustomData[chartKey].shouldRemoveStatsInfoElement = false;
            },
          },
        },
      },
    },
    series: [],
    yhDefinedConfig: {
      name: Helper.titlelize(chartType),
      chartType: chartType,
      chartKey: chartKey,
      resultsAxis: "y",
      logScaleAxis: "y",
      default: settings,
    },
  });

  useLocalBoxPlotData({
    isLocalDataOnly,
    chartType,
    pageKey,
    prerenderData,
    popPendingLocalChartData,
    setShouldFetchChartData,
    setExternalLocalData,
  });

  useEffect(() => {
    if (typeof Highcharts !== "object") {
      message.warning("Highcharts not loaded.", 10);
    }
    chartComponentRefs[chartKey] = ref;

    return () => {
      queryClient.removeQueries({
        queryKey: QueryKeys.chart(pageKey, chartKey),
      });
    };
  }, []);

  React.useImperativeHandle(ref, () => ({
    reloadChart: () => {
      chartDataQuery.refetch();
    },
    // Update chart using client-provided data (bypasses fetch)
    setLocalChartData: (data) => {
      setShouldFetchChartData(false);
      setExternalLocalData(data);
    },
    // Show/Hide loading overlay from parent components
    showLoading: (message = "Loading...") => {
      chartRef.current?.chart?.showLoading(message);
    },
    hideLoading: () => {
      chartRef.current?.chart?.hideLoading();
    },
  }));

  useEffect(() => {
    if (hasTopFailingTests) {
      if (component.props.params.body_params.test_number !== undefined) {
        setTopFailingTestData({
          test_number: component.props.params.body_params.test_number,
        });
      } else {
        Helper.getLotTopFailingTestData(
          urlParams[pageKey].src_type,
          urlParams[pageKey].src_value,
          urlParams[pageKey].mfg_process,
          setTopFailingTestData,
          message.warning,
          message.error,
          cacheData,
        );
      }
    }
  }, []);

  useEffect(() => {
    if (hasTopFailingTests) {
      setShouldFetchChartData(topFailingTestData !== undefined);
    } else {
      setShouldFetchChartData(true);
    }
  }, [topFailingTestData]);

  /**
   * Fetch chart data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchChartData = async ({ signal }) => {
    chartRef.current?.chart?.showLoading();

    if (typeof topFailingTestData?.test_number !== "undefined") {
      filters[pageKey].tNum = topFailingTestData.test_number;
    }
    let allFilters = { ...filters[pageKey], ...chartFilters, ...prerenderData };
    const url = Helper.parseUrlEndpoint(params.url_endpoint, allFilters);
    const requestParams = Helper.filterObjectByKeys(
      allFilters,
      Object.keys(params.body_params),
    );

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Fetch the default chart options of this chart type
   */
  const defaultChartOptions = useChartDefaultOptions(chartType);
  /**
   * Set chart options after all required data are available
   *
   * @param {object} data
   */
  const setOptions = (data) => {
    const boxplotData = data.boxplot_data ?? data.data;
    if (typeof setHasChartData === "function") {
      setHasChartData(Array.isArray(boxplotData) && boxplotData.length > 0);
    }
    chartData.current = data;
    // Use template-defined subtitle if present; otherwise fall back to results subtitle
    const subtitleTemplate = settings?.subtitle;
    options.subtitle.text = subtitleTemplate
      ? ChartHelper.generateChartTitle(
          subtitleTemplate,
          merge({}, filters[pageKey], prerenderData),
        )
      : getResultsSubtitle(data, filters[pageKey]);
    options.xAxis[0].categories = data.x_categories;
    chartCustomData[chartKey].categories = data.x_categories;
    chartCustomData[chartKey].highlightCategories =
      data?.highlight_x_categories;
    setPlotBands(data.plot_band_data, options);

    // Build boxplot stats without mutating the original input
    let cleanedBoxplotData = boxplotData;
    const boxplotStats = Array.isArray(boxplotData)
      ? boxplotData.map((seriesData) => {
          return seriesData.map((point) => {
            const statsData = {
              mean: Array.isArray(point) ? point[5] : undefined,
              stdev: Array.isArray(point) ? point[6] : undefined,
            };
            return statsData;
          });
        })
      : [];
    cleanedBoxplotData = Array.isArray(boxplotData)
      ? boxplotData.map((seriesData) =>
          seriesData.map((point) =>
            Array.isArray(point) ? point.slice(0, 5) : point,
          ),
        )
      : boxplotData;

    const boxPlotSeries = getBoxPlotSeries(
      cleanedBoxplotData,
      data?.legend?.boxplot ?? data.chart_legend ?? settings.boxplot.title,
      boxplotStats,
      data.x_categories,
    );
    const scatterSeries = data.scatter_data
      ? getScatterSeries(data.scatter_data, data?.legend?.outliers)
      : [];
    // Extra series for per-category normalized limits (short red lines)
    if (data?.extra_series) {
      const mkSeries = (arr, name) => {
        if (!Array.isArray(arr)) return null;
        const sdata = arr.map((y, i) =>
          y === null || y === undefined ? null : { x: i, y },
        );
        return {
          type: "scatter",
          name,
          color: name.includes("LoLim") ? "#d9534f" : "#c9302c", // distinct shades for Lo vs Hi
          enableMouseTracking: false,
          showInLegend: true,
          data: sdata,
          marker: {
            symbol: "hline",
            lineColor: name.includes("LoLim") ? "#d9534f" : "#c9302c",
            lineWidth: 2,
            radius: 6,
            fillColor: name.includes("LoLim") ? "#d9534f" : "#c9302c",
          },
        };
      };
      const loSeries = mkSeries(data.extra_series.norm_lo, "|LoLim|");
      const hiSeries = mkSeries(data.extra_series.norm_hi, "|HiLim|");
      if (loSeries) scatterSeries.push(loSeries);
      if (hiSeries) scatterSeries.push(hiSeries);
    }
    options.series = concat(boxPlotSeries, scatterSeries);
    if (data.scatter_data) {
      options.plotOptions = {
        series: {
          stacking: settings?.seriesStacking ?? "overlap",
          marker: {
            fillColor: "#FFFFFF",
            lineColor: null,
            lineWidth: 1,
          },
        },
      };
    }
    setAxisMinMax(
      options.yAxis[0],
      data,
      settings.should_use_limits_scaling ?? true,
    );
    if (settings?.y_axis?.show_plot_lines !== false) {
      options.yAxis[0].plotLines = [
        ...ChartHelper.getPlotLines(
          data,
          ChartHelper.filterPlotLines(DEFAULT_PLOTLINES, chartFilters),
        ),
        ...ChartHelper.getSigmaLines(data),
      ];
    }
    // Append the test data to the user options
    options.testData = ChartHelper.setTestDataToUserOptions(data);
    ChartHelper.updateAxisTitleWithActualValue(data, options.yAxis[0]);
    const chartOptions = defaultChartOptions.data?.data?.value
      ? merge(
          options,
          JSON.parse(defaultChartOptions.data.data.value),
          retainedChartOptions[chartKey] ?? {},
        )
      : options;

    if (settings.plotarea_only) {
      ChartHelper.setPlotAreaOnlyOptions(chartOptions);
    }

    setChartOptions(chartOptions);
  };

  // If chart data is directly set (via prop), render immediately
  useEffect(() => {
    if (localChartData) {
      setOptions(localChartData);
    }
  }, [localChartData]);

  // If chart data is provided via ref setter, render immediately
  useEffect(() => {
    if (externalLocalData) {
      setOptions(externalLocalData);
    }
  }, [externalLocalData]);

  /**
   * Query to fetch chart data
   */
  const chartDataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey, filters),
    queryFn: fetchChartData,
    enabled:
      !isLocalDataOnly &&
      shouldFetchChartData &&
      defaultChartOptions.isSuccess &&
      typeof localChartData === "undefined" &&
      !externalLocalData,
  });

  useEffect(() => {
    if (chartDataQuery.isSuccess && !externalLocalData && !isLocalDataOnly) {
      const response = chartDataQuery.data;
      if (response.success) {
        setOptions(response.data);
      } else {
        notificationApi.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.dataUpdatedAt, externalLocalData]);

  useEffect(() => {
    if (chartDataQuery.isError) {
      notificationApi.error({
        message: component.display_name,
        description: chartDataQuery.error,
      });
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.isError]);

  // If local data is injected, prefer it and disable fetch loop
  useEffect(() => {
    if (externalLocalData && defaultChartOptions.isSuccess) {
      setShouldFetchChartData(false);
      setOptions(externalLocalData);
    }
  }, [externalLocalData, defaultChartOptions.dataUpdatedAt]);

  /**
   * A callback function for the created chart
   *
   * @param {object} chart
   */
  const onChartLoaded = (chart) => {
    chart.shouldRenderStatsInfo = settings.has_stats_info !== false;
    if (chartCustomData) {
      ChartHelper.updateExportingMenu(
        chart,
        setIsChartOptionsOpen,
        setCurrentChart,
        chartCustomData[chartKey],
        fullScreenHandle,
      );
    }
    if (typeof setHighchartsChart === "function") {
      setHighchartsChart(chart);
    }
  };

  return (
    <div>
      {contextHolder}
      {typeof Highcharts === "object" ? (
        chartOptions ? (
          <HighchartsReact
            ref={chartRef}
            highcharts={Highcharts}
            options={chartOptions}
            callback={onChartLoaded}
            containerProps={{
              style: {
                // needed to fix highcharts react wrapper bug where chart does not display the original size after exiting full screen
                height: chartRef.current
                  ? chartRef.current.chart.chartHeight
                  : settings.height,
              },
            }}
          />
        ) : (
          <ChartLoading height={settings.height} />
        )
      ) : (
        <Empty description="Highcharts not loaded"></Empty>
      )}
    </div>
  );
});
