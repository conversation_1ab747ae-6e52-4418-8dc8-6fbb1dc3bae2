import Highcharts from "highcharts";
import { renderToString } from "react-dom/server";
import { merge, uniq } from "lodash";
import Helper from "../helper";
import { UserSettingsKeys } from "../user_settings_keys";
import { DEFAULT_PLOTLINES } from "./constants";
import { getHistogramDataSeries } from "./chart_common";

const parametricColors = [
  "#0024FF",
  "#044AFF",
  "#046FFF",
  "#0193FF",
  "#01B8FF",
  "#03DDFF",
  "#04FFFD",
  "#4CFFB3",
  "#71FF8E",
  "#44FF70",
  "#96FF69",
  "#BAFF45",
  "#E9FF20",
  "#FFFA03",
  "#FFD503",
  "#FFB002",
  "#FF8B02",
  "#FF6701",
  "#FF4203",
  "#FF1D00",
];
const hiddenColor = "#CCCCCC";

/**
 * Set highcharts options globally
 */
const setHighchartsOptions = () => {
  Highcharts.setOptions({
    credits: {
      enabled: false,
    },
    subtitle: {
      align: "center",
    },
    chart: {
      animation: false,
      panning: {
        enabled: true,
        type: "xy",
      },
      panKey: "shift",
      events: {
        beforePrint: function () {
          if (this.legend.pages.length > 0) {
            this.resetParams = [this.chartWidth, this.chartHeight, false];
            var height =
              this.chartHeight +
              this.legend.fullHeight * this.legend.pages.length;
            if (
              typeof this.legend.options === "object" &&
              typeof this.legend.options.maxHeight === "number"
            ) {
              this.legendMaxHeight = this.legend.options.maxHeight;
              this.legend.options.maxHeight = null;
            }
            this.setSize(this.chartWidth, height, false);
          }
        },
        afterPrint: function () {
          if (this.resetParams) {
            if (typeof this.legendMaxHeight === "number") {
              this.legend.options.maxHeight = this.legendMaxHeight;
            }
            this.setSize.apply(this, this.resetParams);
          }
        },
      },
    },
    title: {
      align: "left",
      style: {
        fontSize: "0.9em",
      },
    },
  });
};

/**
 * Enter/exit chart in full screen
 *
 * @param {object} chart
 * @param {object} customData - chart custom data
 * @param {object} fullScreenHandle
 */
const toggleFullScreen = (chart, customData, fullScreenHandle) => {
  if (customData.fullscreen) {
    if (customData.fullscreen.state) {
      fullScreenHandle.exit();
    } else {
      customData.fullscreen.containerHeight = chart.renderTo.clientHeight;
      customData.fullscreen.containerWidth = chart.renderTo.clientWidth;
      customData.fullscreen.origHeight = chart.chartHeight;
      customData.fullscreen.spacingBottom = chart.spacing[2];
      fullScreenHandle.enter();
    }
  } else {
    ChartHelper.updateFullScreenViewLabel(chart, !chart.fullscreen.isOpen);
    chart.fullscreen.toggle();
  }
};

/**
 * Generate highcharts menu
 *
 * @param {function} setIsChartOptionsOpen
 * @param {function} setCurrentChart
 * @param {object} customData - chart custom data
 * @param {object} fullScreenHandle
 * @returns {object} menu
 */
const generateHighchartsMenu = (
  setIsChartOptionsOpen,
  setCurrentChart,
  customData,
  fullScreenHandle,
) => {
  const menuItems = [
    "chartOptions",
    "separator",
    "fullScreenView",
    "separator",
    "downloadPNG",
    "downloadJPEG",
    "downloadPDF",
    "downloadSVG",
  ];
  let menuItemDefinitions = {
    // Custom definition
    chartOptions: {
      onclick: function () {
        setCurrentChart(this);
        setIsChartOptionsOpen(true);
      },
      text: "Chart Options",
    },
    fullScreenView: {
      onclick: function () {
        toggleFullScreen(this, customData, fullScreenHandle);
      },
      text: "View in full screen",
    },
  };

  let menu = {
    menuItemDefinitions: menuItemDefinitions,
    buttons: {
      contextButton: {
        menuItems: menuItems,
      },
    },
    chartOptions: {
      legend: {
        navigation: {
          enabled: false,
        },
      },
    },
  };

  return menu;
};

/**
 * Get the line marks, e.g. limits and mean
 *
 * @param {object} testStatsInfo The test statistics
 * @param {object} values The values from the chart options form
 * @returns {object}
 */
const getLineMarks = (testStatsInfo, values) => {
  if (typeof testStatsInfo === "undefined") {
    return;
  }
  const marksDef = [
    {
      name: "lo_lim",
      label: "LL",
      align: "left",
      propertyName: "testLimits",
    },
    {
      name: "hi_lim",
      label: "HL",
      align: "right",
      propertyName: "testLimits",
    },
    {
      name: "iqr_lo_lim",
      label: "RL",
      align: "left",
      propertyName: "robustLimits",
    },
    {
      name: "iqr_hi_lim",
      label: "RH",
      align: "right",
      propertyName: "robustLimits",
    },
    {
      name: "mean",
      label: "Mean",
      align: "center",
      propertyName: "meanMark",
    },
  ];
  return (
    marksDef
      // .filter((markObj) => testStatsInfo[`${markObj.name}_valid`])
      .map((markObj) => {
        return {
          width: values[markObj.propertyName] ? 1 : 0,
          dashStyle: "dash",
          color: "red",
          value: +testStatsInfo[markObj.name],
          zIndex: 3,
          label: {
            text: values[markObj.propertyName]
              ? `${markObj.label}: ${testStatsInfo[markObj.name]}`
              : "",
            align: markObj.align,
            style: {
              color: "red",
            },
          },
        };
      })
  );
};

/**
 * Generate chart options from form values
 *
 * @param {object} values
 * @param {Highcharts.Chart} chart
 * @returns {object} chartOptions
 */
const generateChartOptionsFromFormValues = (values, chart, testStatsInfo) => {
  const chartOptions = {
    title: {},
    chart: {},
    xAxis: {
      title: {},
      labels: {},
    },
    yAxis: [
      {
        title: {},
        plotLines: getLineMarks(testStatsInfo, values),
      },
    ],
    series: [],
  };
  if (values.y2) {
    chartOptions.yAxis.push({});
    if (values.y2.title) {
      chartOptions.yAxis[1].title = {};
    }
  }

  let columnSeriesIndex;
  let lineSeriesIndex;
  Object.keys(values).forEach((key) => {
    if (values[key] === undefined) {
      return;
    }
    switch (key) {
      case "title":
        if (chartOptions.title === undefined) {
          chartOptions.title = {};
        }
        chartOptions.title.text = values[key];
        break;
      case "width":
        chartOptions.chart.width = values[key];
        break;
      case "height":
        chartOptions.chart.height = values[key];
        break;
      case "minRange":
        chartOptions.yAxis[0].min = parseFloat(values[key]);
        break;
      case "maxRange":
        chartOptions.yAxis[0].max = parseFloat(values[key]);
        break;
      case "bellCurve":
        chartOptions.series[2] = {
          visible: values[key],
          showInLegend: values[key],
        };
        break;
      case "x":
        Object.keys(values[key]).forEach((subKey) => {
          switch (subKey) {
            case "title":
              chartOptions.xAxis.title.text = values[key][subKey] ?? "";
              break;
            case "gridLines":
              chartOptions.xAxis.gridLineWidth = values[key][subKey] ? 1 : 0;
              break;
            case "labelRotation":
              chartOptions.xAxis.labels.rotation = parseInt(
                values[key][subKey],
              );
              break;
          }
        });
        break;
      case "y":
        Object.keys(values[key]).forEach((subKey) => {
          switch (subKey) {
            case "title":
              chartOptions.yAxis[0].title.text = values[key][subKey];
              break;
            case "gridLines":
              chartOptions.yAxis[0].gridLineWidth = values[key][subKey] ? 1 : 0;
              break;
            case "logScale":
              chartOptions.yAxis[0].type = values[key][subKey]
                ? "logarithmic"
                : "linear";
              break;
          }
        });
        break;
      case "y2":
        Object.keys(values[key]).forEach((subKey) => {
          switch (subKey) {
            case "title":
              chartOptions.yAxis[1].title.text = values[key][subKey];
              break;
            case "gridLines":
              chartOptions.yAxis[1].gridLineWidth = values[key][subKey] ? 1 : 0;
              break;
            case "minRange":
              chartOptions.yAxis[1].min = parseFloat(values[key][subKey]);
              break;
            case "maxRange":
              chartOptions.yAxis[1].max = parseFloat(values[key][subKey]);
              break;
          }
        });
        break;
      case "bar":
        columnSeriesIndex = getChartSeriesIndex(chart, "column");
        if (columnSeriesIndex !== null) {
          if (chartOptions.series[columnSeriesIndex] === undefined) {
            chartOptions.series[columnSeriesIndex] = {};
          }
          Object.keys(values[key]).forEach((subKey) => {
            switch (subKey) {
              case "title":
                chartOptions.series[columnSeriesIndex].name =
                  values[key][subKey];
                break;
            }
          });
        }
        break;
      case "line":
        lineSeriesIndex = getChartSeriesIndex(chart, "line");
        if (lineSeriesIndex !== null) {
          if (chartOptions.series[lineSeriesIndex] === undefined) {
            chartOptions.series[lineSeriesIndex] = {};
          }
          Object.keys(values[key]).forEach((subKey) => {
            switch (subKey) {
              case "title":
                chartOptions.series[lineSeriesIndex].name = values[key][subKey];
                break;
            }
          });
        }
        break;
      case "legend":
        Object.keys(values[key]).forEach((subKey) => {
          switch (subKey) {
            case "color":
              Object.keys(values[key][subKey]).forEach((seriesKey) => {
                if (values[key][subKey][seriesKey]) {
                  if (chartOptions.series[seriesKey] === undefined) {
                    chartOptions.series[seriesKey] = {};
                  }
                  chartOptions.series[seriesKey].color =
                    values[key][subKey][seriesKey];
                }
              });
              break;
          }
        });
        break;
    }
  });

  return chartOptions;
};

/**
 * Get chart series index based on type
 *
 * @param {Highcharts.Chart} chart
 * @param {string} type
 * @returns {int|null} seriesIndex
 */
const getChartSeriesIndex = (chart, type) => {
  let seriesIndex = null;
  Object.keys(chart.series).forEach((key) => {
    if (chart.series[key].type === type) {
      seriesIndex = key;
    }
  });

  return seriesIndex;
};

/**
 * Get parametric color axis colors based on band count
 *
 * @param {int} bandCount
 * @returns {array} colors
 */
const getParametricColors = (bandCount) => {
  let colors = parametricColors;
  switch (bandCount) {
    case 10:
      colors = [
        "#0024FF",
        "#046FFF",
        "#01B8FF",
        "#4CFFB3",
        "#44FF70",
        "#96FF69",
        "#BAFF45",
        "#FFFA03",
        "#FF8B02",
        "#FF1D00",
      ];
      break;
    case 5:
      colors = ["#0024FF", "#01B8FF", "#71FF8E", "#FFFA03", "#FF1D00"];
      break;
    case 2:
      colors = ["#0024FF", "#FF1D00"];
      break;
  }

  return colors;
};

/**
 * Get result count within band
 *
 * @param {array} results
 * @param {number} from
 * @param {number} to
 * @returns {int} count
 */
const getResultCountWithinBand = (results, from, to) => {
  const count = results.filter((result) => {
    return from === undefined
      ? result <= to
      : to === undefined
        ? result > from
        : result > from && result <= to;
  }).length;

  return count;
};

/**
 * Get stats info width
 *
 * @param {object} chart
 * @returns {number} width
 */
const getStatsInfoWidth = (chart) => {
  const width = chart.chartWidth - chart.plotLeft;

  return width;
};

/**
 * Get wafer info width
 *
 * @param {object} chart
 * @returns {number} width
 */
const getWaferInfoWidth = (chart) => {
  const width = chart.chartWidth - chart.plotLeft;

  return width;
};

/**
 * Remove rendered wafer info and space allocated
 *
 * @param {object} chart
 * @param {object} options
 * @param {object} customData
 * @param {boolean} redraw
 */
const removeWaferInfo = (chart, options, customData, redraw = false) => {
  customData.waferInfoElement.forEach((element) => {
    element.remove();
  });
  // resize chart to its original height if not in full screen mode
  if (!customData.isFullScreen) {
    options.chart.height =
      (customData.origChartHeight ?? chart.chartHeight) -
      customData.waferInfoHeight;
    chart.renderTo.style.height = `${options.chart.height}px`;
    chart.setSize(chart.chartWidth, options.chart.height, redraw);
  }
};

/**
 * Generate stats info to display
 *
 * @param {object} chart
 * @param {object} stats
 * @param {object} data
 * @returns {string} statsInfo
 */
const getStatsInfo = (chart, stats, data) => {
  const statsSignificantDigits = {};
  Helper.getUserSettings(
    UserSettingsKeys.significant_digit_settings_grid,
    "sitewide.preferences.appearance",
    {},
  )?.forEach((item) => {
    statsSignificantDigits[item.key] = item;
  });

  const statsInfoStyle =
    "border: 1px solid #D9D9D9; background: #FAFAFA; padding: 4px; white-space: nowrap;";
  const statsLabelStyle = "font-weight: 600;";
  const statsInfoArr = Object.keys(stats).map((key) => {
    return `<div style="${statsInfoStyle}"><span style="${statsLabelStyle}">${stats[key]}:</span> ${!isNaN(Number(data[key])) ? Helper.descriptiveFormatting(data[key] ?? "-", 4, statsSignificantDigits[key]?.chart ?? 4) : (data[key] ?? "-")}</div>`;
  });
  const statsInfo = `
    <div style="width: ${getStatsInfoWidth(chart)}px; display: flex; flex-wrap: wrap; row-gap: 8px; column-gap: 8px;">
      ${statsInfoArr.join("")}
    </div>
    `;

  return statsInfo;
};

/**
 * Get the histogram scaling data required in calculating the histogram data
 *
 * @param {object} data
 * @param {object} param
 * @returns {array}
 */
const getHistogramScalingData = (data, param = {}) => {
  const getMinMaxResults = (data) => {
    let min = null,
      max = null;
    (data.results || []).forEach((res) => {
      const numericRes = res.filter((val) => typeof val === "number");
      const minRes = Math.min(...numericRes);
      const maxRes = Math.max(...numericRes);
      if (min === null || minRes < min) min = minRes;
      if (max === null || maxRes > max) max = maxRes;
    });
    return [min, max];
  };

  const [defaultMin, defaultMax] = getMinMaxResults(data);
  let min = parseFloat(param.x_min_range ?? data.min_result ?? defaultMin);
  let max = parseFloat(param.x_max_range ?? data.max_result ?? defaultMax);
  let minLimit = data.lo_lim ?? null;
  let maxLimit = data.hi_lim ?? null;
  const hasMinLimit = !isNaN(minLimit) && minLimit >= min;
  const hasMaxLimit = !isNaN(maxLimit) && maxLimit <= max;

  if (min === 0 && max === 0) {
    min = -1.0;
    max = 1.0;
  } else if (min === max) {
    const multiplier = min < 0 ? -0.2 : 0.2;
    min -= min * multiplier;
    max += max * multiplier;
  } else if (min > max) {
    [min, max] = [max, min];
  }

  return [min, max, minLimit, maxLimit, hasMinLimit, hasMaxLimit];
};

/**
 * Get the histogram custom slot size and min/max data required in calculating the histogram data
 *
 * @param {int} slotCount
 * @param {float} min
 * @param {float} max
 * @param {float} minLimit
 * @param {float} maxLimit
 * @param {boolean} hasMinLimit
 * @param {boolean} hasMaxLimit
 * @returns {array}
 */
const getHistogramCustomSlotSizeAndMinMaxData = (
  slotCount,
  min,
  max,
  minLimit,
  maxLimit,
  hasMinLimit,
  hasMaxLimit,
) => {
  let slotSize = (max - min) / slotCount;

  if (hasMinLimit && !hasMaxLimit) {
    maxLimit = minLimit + slotSize;
  } else if (hasMaxLimit && !hasMinLimit) {
    minLimit = maxLimit - slotSize;
  } else if (maxLimit - minLimit < slotSize) {
    maxLimit = minLimit + slotSize;
  }

  if (minLimit > maxLimit) {
    [minLimit, maxLimit] = [maxLimit, minLimit];
  }

  let minLimitSlotIndex = (minLimit - min) / slotSize;
  let maxLimitSlotIndex = (maxLimit - min) / slotSize;

  minLimitSlotIndex = Number(minLimitSlotIndex.toPrecision(15));
  maxLimitSlotIndex = Number(maxLimitSlotIndex.toPrecision(15));

  minLimitSlotIndex = Number.isFinite(minLimitSlotIndex)
    ? Math.floor(minLimitSlotIndex)
    : 0;
  maxLimitSlotIndex = Number.isFinite(maxLimitSlotIndex)
    ? Math.floor(maxLimitSlotIndex)
    : 0;

  if (minLimitSlotIndex === maxLimitSlotIndex) {
    maxLimitSlotIndex += 1;
  }

  slotSize = (maxLimit - minLimit) / (maxLimitSlotIndex - minLimitSlotIndex);
  const minX = minLimit - minLimitSlotIndex * slotSize;
  const maxX = maxLimit + (slotCount - maxLimitSlotIndex) * slotSize;

  return [slotSize, minX, maxX];
};

/**
 * Get the histogram slot size and min/max data required in calculating the histogram data
 *
 * @param {float} min
 * @param {float} max
 * @param {float} minLimit
 * @param {float} maxLimit
 * @param {boolean} hasMinLimit
 * @param {boolean} hasMaxLimit
 * @param {object} param
 * @returns {array}
 */
const getHistogramSlotSizeAndMinMaxData = (
  min,
  max,
  minLimit,
  maxLimit,
  hasMinLimit,
  hasMaxLimit,
  param,
) => {
  const slotCount = 48;
  const desiredSlotCount = param.bin_count ?? null;
  const desiredSlotSize = param.bin_width ?? null;

  let slotSize, minX, maxX;

  if (desiredSlotCount) {
    slotSize = (max - min) / desiredSlotCount;
    minX = Math.floor(min / slotSize) * slotSize;
    maxX = Math.ceil(max / slotSize) * slotSize;
  } else if (desiredSlotSize) {
    slotSize = desiredSlotSize;
    minX = Math.floor(min / slotSize) * slotSize;
    maxX = Math.ceil(max / slotSize) * slotSize;
  } else if (!hasMinLimit && !hasMaxLimit) {
    slotSize = (max - min) / slotCount;
    minX = Math.floor(min / slotSize) * slotSize;
    maxX = Math.ceil(max / slotSize) * slotSize;
  } else {
    [slotSize, minX, maxX] = getHistogramCustomSlotSizeAndMinMaxData(
      slotCount,
      min,
      max,
      minLimit,
      maxLimit,
      hasMinLimit,
      hasMaxLimit,
    );
  }

  return [slotSize, minX, maxX];
};

/**
 * Get the histogram slot count required in calculating the histogram data
 *
 * @param {float} min
 * @param {float} max
 * @param {float} minX
 * @param {float} maxX
 * @param {float} slotSize
 * @returns {int}
 */
const getHistogramSlotCount = (min, max, minX, maxX, slotSize) => {
  while (minX > min) minX -= slotSize;
  while (maxX < max) maxX += slotSize;

  return Math.round((maxX - minX) / slotSize);
};

/**
 * Calculates basic descriptive statistics on a numeric dataset
 *
 * @param {array} values
 * @returns {object}
 */
const describe = (values) => {
  const n = values.length;
  const mean = values.reduce((a, b) => a + b, 0) / n;
  const sd = Math.sqrt(values.reduce((a, b) => a + (b - mean) ** 2, 0) / n);
  return { n, mean, sd };
};

/**
 * Get the histogram chart grouped data
 *
 * @param {object} data
 * @returns {object}
 */
const getHistogramGroupedData = (data) => {
  const groupedData = {
    x_categories: [],
    results: [],
    lo_lim: null,
    hi_lim: null,
  };
  let aggResults = [];

  if (Array.isArray(data[Object.keys(data)[0]]?.results)) {
    for (const key in data) {
      const token = data[key];
      const results = (token.results ?? []).filter(
        (val) => typeof val === "number",
      );
      aggResults = aggResults.concat(results);
      groupedData.results.push(results);
      groupedData.lo_lim ??= token.lo_lim ?? null;
      groupedData.hi_lim ??= token.hi_lim ?? null;
      delete data[key];
    }
  } else if (Array.isArray(data.results?.[0])) {
    for (let i = 0; i < data.results.length; i++) {
      const results = (data.results[i] ?? []).filter(
        (val) => typeof val === "number",
      );
      aggResults = aggResults.concat(results);
      groupedData.results.push(results);
    }
    groupedData.lo_lim = data.lo_lim ?? null;
    groupedData.hi_lim = data.hi_lim ?? null;
  }

  const stats = describe(aggResults);
  groupedData.execs_overall = stats.n;
  groupedData.mean = stats.mean;
  groupedData.stdev = stats.sd;

  return groupedData;
};

/**
 * Calculate the histogram chart data format given the results and parameters
 *
 * @param {object} groupedData
 * @param {object} param
 * @param {string} type
 * @returns {array}
 */
const calculateHistogramData = (groupedData, param, type = "bar") => {
  const yData = [];
  const legend = [];
  const dataPointsCount = [];

  const results = groupedData.results ?? [];
  const legends = groupedData.x_categories ?? [];
  const fillValue = 0.0;
  const slotCount = (param.slot_count ?? 0) + 1;

  results.forEach((groupResults, groupIndex) => {
    const frequency = new Array(slotCount).fill(fillValue);

    groupResults.forEach((value) => {
      const slotSize = param.slot_size ?? 1;
      const minX = param.min_x ?? 0;
      const slotIndex = Math.floor((value - minX) / slotSize);

      // Handle slot assignment logic
      if (slotIndex >= 0 && slotIndex < slotCount) {
        frequency[slotIndex]++;
      } else if (slotIndex >= param.slot_count) {
        frequency[param.slot_count - 1]++;
      }
    });

    // Convert to percentage if required
    if (param.y_as_pct ?? false) {
      dataPointsCount[groupIndex] = [...frequency]; // Copy before conversion
      const sum = frequency.reduce((a, b) => a + b, 0);
      for (let i = 0; i < frequency.length; i++) {
        frequency[i] = sum > 0 ? (frequency[i] / sum) * 100.0 : 0.0;
      }
    }

    // Bar chart: replace 0.0 with null
    if (type === "bar") {
      for (let i = 0; i < frequency.length; i++) {
        if (frequency[i] === 0.0) {
          frequency[i] = null;
        }
      }
    }

    yData[groupIndex] = frequency;
    legend[groupIndex] = legends[groupIndex] ?? `Group ${groupIndex + 1}`;
  });

  return [yData, legend, dataPointsCount];
};

/**
 * Chart helper object
 */
const ChartHelper = {
  /**
   * Initialize highcharts
   *
   * @param {function} setIsChartOptionsOpen
   * @param {function} setCurrentChart
   */
  initHighcharts: (setIsChartOptionsOpen, setCurrentChart) => {
    setHighchartsOptions(setIsChartOptionsOpen, setCurrentChart);
  },
  /**
   * Initialize chart custom data
   *
   * @param {object} chartCustomData
   * @param {string} chartKey
   * @param {boolean} useFullScreen
   */
  initChartCustomData: (chartCustomData, chartKey, useFullScreen = true) => {
    if (!chartCustomData[chartKey]) {
      if (useFullScreen) {
        chartCustomData[chartKey] = {
          fullscreen: {
            state: false,
            containerHeight: 0,
            containerWidth: 0,
            origHeight: 0,
            spacingBottom: 0,
          },
        };
      } else {
        chartCustomData[chartKey] = {};
      }
    }
  },
  /**
   * Update chart options from chart options form values
   *
   * @param {object} chartOptionsValues
   * @param {Highcharts.Chart} chart
   * @param {function} setCurrentChart
   * @param {object} currentChartSettings
   * @param {function} setCurrentChartSettings
   * @param {object} testStatsInfo The test stats info
   */
  updateChartOptions: (
    chartOptionsValues,
    chart,
    setCurrentChart,
    currentChartSettings,
    setCurrentChartSettings,
    testStatsInfo,
  ) => {
    const chartOptions = generateChartOptionsFromFormValues(
      chartOptionsValues,
      chart,
      testStatsInfo,
    );
    if (chartOptions.chart) {
      const width = chartOptions.chart.width ? chartOptions.chart.width : null;
      const height = chartOptions.chart.height
        ? chartOptions.chart.height
        : "auto";
      chart.setSize(width, height);
    }
    chart.update(chartOptions);
    setCurrentChart(chart);
    setCurrentChartSettings(
      Object.assign(currentChartSettings, chartOptionsValues),
    );
  },
  /**
   * Replace dynamic axis title with actual value
   *
   * @param {object} chartData
   * @param {object} axis
   */
  updateAxisTitleWithActualValue: (chartData, axis) => {
    // Object.keys(chartData).forEach((key) => {
    //   if (axis.title.text === `$\{${key}}`) {
    //     axis.title.text = chartData[key];
    //   }
    // });

    const titleText = axis.title.text;
    // Replace all occurrences of ${key} with corresponding values
    axis.title.text = titleText.replace(/\$\{([^}]+)\}/g, (match, key) => {
      return chartData[key] !== undefined ? chartData[key] : match;
    });
  },
  /**
   * Create series data from x and y values
   *
   * @param {array} xData
   * @param {array} yData
   * @returns {array} seriesData
   */
  generateSeriesDataFromXYValues: (xData, yData) => {
    let seriesData = [];
    xData.forEach((x, index) => {
      seriesData.push([x, yData[index]]);
    });

    return seriesData;
  },
  /**
   * Create color axis scale of parametric wafer map
   *
   * @param {number} minValue
   * @param {number} maxValue
   * @param {array} values
   * @param {int} bandCount
   * @param {array} selectedColorScale
   * @param {int} decimalPlaces
   * @returns {array} colorScale
   */
  generateParametricColorScale: (
    minValue,
    maxValue,
    values,
    bandCount,
    selectedColorScale = [],
    decimalPlaces = 4,
  ) => {
    let colorScale = [];
    const colors = getParametricColors(bandCount);
    const diff = (maxValue - minValue) / bandCount;
    const step = Math.pow(10, decimalPlaces * -1);
    for (let i = 0; i < bandCount; i++) {
      let scale = {
        color:
          selectedColorScale.length > 0 &&
          selectedColorScale.indexOf(colors[i]) === -1
            ? hiddenColor
            : colors[i],
      };
      scale.from = parseFloat(minValue.toFixed(decimalPlaces) + step);
      scale.to = parseFloat((minValue + diff).toFixed(decimalPlaces));
      const resultCount = getResultCountWithinBand(
        values,
        scale.from,
        scale.to,
      );
      scale.name = resultCount;
      colorScale.push(scale);
      minValue += diff;
    }
    colorScale.reverse();

    return colorScale;
  },

  /**
   * Draw high and low limit lines
   *
   * @param {object} chartData
   * @returns {array} plotLines
   */
  drawLimitLines: (chartData) => {
    let plotLines = [];
    const loLimKey = "lo_lim";
    const hiLimKey = "hi_lim";
    const limits = {
      [loLimKey]: {
        labelText: "LL",
        labelAlign: "right",
      },
      [hiLimKey]: {
        labelText: "HL",
        labelAlign: "left",
      },
    };
    Object.keys(limits).forEach((limitKey) => {
      if (chartData[limitKey] !== undefined) {
        plotLines.push({
          value: chartData[limitKey],
          dashStyle: "dash",
          color: "red",
          width: 1,
          label: {
            text: `${limits[limitKey].labelText}: ${chartData[limitKey]}`,
            align: `${limits[limitKey].labelAlign}`,
            style: {
              color: "red",
            },
          },
        });
      }
    });

    return plotLines;
  },
  /**
   * Default settings of the charts
   *
   * @param {boolean} useDefault - use default settings or not
   * @param {boolean} boostMode Whether to use chart level boost mode
   * @returns {object} settings
   */
  getChartDefaultSettings: (useDefault = true, boostMode = false) => {
    let settings = {};
    if (useDefault) {
      settings = {
        chart: {
          zoomType: "xy",
        },
        tooltip: {
          enabled: true,
        },
        xAxis: [
          {
            labels: {
              rotation: 300,
            },
            gridLineWidth: 0,
          },
        ],
        yAxis: [
          {
            type: "linear",
            gridLineWidth: 1,
          },
        ],
        // Limit the legend to around 3 rows per page
        legend: {
          maxHeight: 85,
        },
      };
    }

    if (boostMode) {
      settings.boost = {
        useGPUTranslations: true,
        seriesThreshold: 1,
      };
    }

    return settings;
  },
  /**
   * Default stats to be displayed below the chart
   *
   * @returns {object}
   */
  getChartDefaultStats: () => {
    return {
      stats_type: "Data",
      mean: "Mean",
      stdev: "Stdev",
      cp: "Cp",
      cpk: "Cpk",
      min_result: "Min",
      max_result: "Max",
      execs_overall: "Execs",
      fails_overall: "Fails",
      fp_pexecs: "%Fail per Exec",
      fp_punits: "%Fail per Unit",
    };
  },
  /**
   * Filter plot lines based on chart filters
   *
   * @param {array} plotLines
   * @param {object} chartFilters
   * @returns {array} plotLines
   */
  filterPlotLines: (plotLines, chartFilters) => {
    if (chartFilters.show_limits === false) {
      plotLines = plotLines.filter((item) => item !== "test_limits");
    }

    return plotLines;
  },
  /**
   * Helper function to remove plot lines and min/max properties from Highcharts options
   * @param {Object||String} chartOptions - The original Highcharts options object
   * @returns {Object} - A new options object with plot lines and min/max removed
   */
  cleanupChartOptions: (chartOptions) => {
    // Deep clone the options to avoid mutating the original
    const newOptions =
      typeof chartOptions === "string"
        ? JSON.parse(chartOptions)
        : JSON.parse(JSON.stringify(chartOptions));

    // Helper function to clean axis options
    const cleanAxisOptions = (axis) => {
      if (!axis) return axis;

      // Remove plotLines if they exist
      delete axis.plotLines;
      // Remove plotBands if they exist
      delete axis.plotBands;
      // Remove min and max properties
      delete axis.min;
      delete axis.max;

      return axis;
    };

    // Clean xAxis (handle both single axis and array of axes)
    if (newOptions.xAxis) {
      if (Array.isArray(newOptions.xAxis)) {
        newOptions.xAxis = newOptions.xAxis.map((axis) =>
          cleanAxisOptions(axis),
        );
      } else {
        newOptions.xAxis = cleanAxisOptions(newOptions.xAxis);
      }
    }

    // Clean yAxis (handle both single axis and array of axes)
    if (newOptions.yAxis) {
      if (Array.isArray(newOptions.yAxis)) {
        newOptions.yAxis = newOptions.yAxis.map((axis) =>
          cleanAxisOptions(axis),
        );
      } else {
        newOptions.yAxis = cleanAxisOptions(newOptions.yAxis);
      }
    }

    return newOptions;
  },
  /**
   * Helper function to filter retainable chart properties from Highcharts options
   *
   * @param {(object|string)} chartOptions - The original Highcharts options object
   * @returns {object} retainedOptions - A new options object with retainable properties
   */
  filterRetainableChartOptions: (chartOptions) => {
    // Deep clone the options to avoid mutating the original
    const retainedOptions =
      typeof chartOptions === "string"
        ? JSON.parse(chartOptions)
        : JSON.parse(JSON.stringify(chartOptions));

    // Helper function to clean axis options
    const cleanAxisOptions = (axis) => {
      if (!axis) return axis;

      // Remove plotLines if they exist
      delete axis.plotLines;
      // Remove plotBands if they exist
      delete axis.plotBands;

      return axis;
    };

    // Clean xAxis (handle both single axis and array of axes)
    if (retainedOptions.xAxis) {
      if (Array.isArray(retainedOptions.xAxis)) {
        retainedOptions.xAxis = retainedOptions.xAxis.map((axis) =>
          cleanAxisOptions(axis),
        );
      } else {
        retainedOptions.xAxis = cleanAxisOptions(retainedOptions.xAxis);
      }
    }

    // Clean yAxis (handle both single axis and array of axes)
    if (retainedOptions.yAxis) {
      if (Array.isArray(retainedOptions.yAxis)) {
        retainedOptions.yAxis = retainedOptions.yAxis.map((axis) =>
          cleanAxisOptions(axis),
        );
      } else {
        retainedOptions.yAxis = cleanAxisOptions(retainedOptions.yAxis);
      }
    }

    return retainedOptions;
  },

  /**
   * Formats a value for display in a plot line label. If the value is a number,
   * it is formatted using descriptive formatting. If the value is a string, it is
   * passed through unchanged. If a maximum width is provided, the value is
   * truncated to fit within that width. The rendered string is returned.
   * @param {number|string} value - The value to be formatted
   * @param {number} [maxWidth=0] - Maximum width of the label in pixels
   * @return {string} Formatted value
   */
  formatPlotLineValue: (value, maxWidth = 0) => {
    const formattedValue =
      typeof value === "number" ? Helper.descriptiveFormatting(value) : value;

    if (maxWidth > 0) {
      const maxLength = Helper.getStringMaxLength(maxWidth * 2 - 60);
      return renderToString(
        Helper.truncateString(formattedValue, maxLength, "middle"),
      );
    }
    return formattedValue;
  },

  /**
   * Generates plot lines configuration for charts based on data and specified parameters
   *
   * @param {Object} data - The data object containing values for plot lines
   * @param {string[]} plotLines - Types of plot lines to include
   * @param {boolean} [vertical=false] - Whether the plot lines should be vertical
   * @param {number} [availableWidth=0] - Available width for label truncation
   * @return {Array<Object>} Array of plot line configurations
   */
  getPlotLines: (
    data = {},
    plotLines = DEFAULT_PLOTLINES,
    vertical = false,
    availableWidth = 0,
  ) => {
    const MARKS_CONFIG = {
      lo_limit: {
        label: "LL",
        align: "right",
        verticalAlign: "top",
        propertyName: "test_limits",
        position: { x: vertical ? -4 : undefined, y: vertical ? 10 : -4 },
      },
      hi_limit: {
        label: "HL",
        align: "left",
        verticalAlign: vertical ? "bottom" : "top",
        propertyName: "test_limits",
        position: { x: vertical ? -5 : 5, y: vertical ? -10 : -4 },
      },
      original_lo_limit: {
        label: "LL2",
        align: "right",
        verticalAlign: "top",
        propertyName: "original_test_limits",
        position: { x: vertical ? -4 : undefined, y: vertical ? 10 : -4 },
      },
      original_hi_limit: {
        label: "HL2",
        align: "left",
        verticalAlign: vertical ? "bottom" : "top",
        propertyName: "original_test_limits",
        position: { x: vertical ? -5 : 5, y: vertical ? -10 : -4 },
      },
      new_lo_lim: {
        label: "NLL",
        align: "right",
        verticalAlign: "top",
        propertyName: "new_test_limits",
        position: { x: vertical ? -4 : undefined, y: vertical ? 10 : -4 },
      },
      new_hi_lim: {
        label: "NHL",
        align: "left",
        verticalAlign: vertical ? "bottom" : "top",
        propertyName: "new_test_limits",
        position: { x: vertical ? -5 : 5, y: vertical ? -10 : -4 },
      },
      robust_lo_limit: {
        label: "RL",
        align: "right",
        verticalAlign: "top",
        propertyName: "robust_limits",
        position: { x: vertical ? -4 : undefined, y: vertical ? 10 : -4 },
      },
      robust_hi_limit: {
        label: "RH",
        align: "right",
        verticalAlign: vertical ? "bottom" : "top",
        propertyName: "robust_limits",
        position: { x: vertical ? -5 : 5, y: vertical ? -10 : -4 },
      },
      mean: {
        label: "Mean",
        align: "center",
        verticalAlign: "middle",
        propertyName: "mean",
        position: {
          x: vertical ? -4 : undefined,
          y: vertical ? undefined : -4,
        },
      },
      dlog_info: {
        label: "",
        align: "center",
        verticalAlign: "middle",
        propertyName: "dlog_info",
        position: {
          x: vertical ? -4 : undefined,
          y: vertical ? undefined : -4,
        },
        style: {
          valueType: "string",
          dashStyle: "solid",
          lineColor: "#d3d3d3",
          labelColor: "#333333",
        },
      },
      zone_info: {
        label: "",
        align: "right",
        textAlign: "right",
        verticalAlign: "top",
        propertyName: "zone_info",
        position: {
          x: vertical ? -4 : undefined,
          y: vertical ? undefined : -4,
        },
        style: {
          valueType: "string",
          dashStyle: "solid",
          lineColor: "#d3d3d3",
          labelColor: "#333333",
        },
      },
    };

    /**
     * Creates a plot line from a mark configuration and a value. If the value is
     * an array, the first element is used as the line value and the second element
     * is used as the label. If the value is a number, it is used as the line value
     * and the same number is used as the label. If the value is a string, it is
     * passed through unchanged. If a maximum width is provided, the label is
     * truncated to fit within that width. If the line value is not a number, the
     * line is hidden. The returned object is a Highcharts plot line options object.
     * @param {object} markConfig - The mark configuration
     * @param {number|string|Array<number|string>} value - The value to be used as the line value and label
     * @param {number} [index=0] - The index of the mark in the series
     * @return {object} Highcharts plot line options object
     */
    const createPlotLine = (markConfig, value, index = 0) => {
      const lineValue = Array.isArray(value) ? value[0] : value;
      const label = Array.isArray(value) ? value[1] : value;
      const formattedValue = ChartHelper.formatPlotLineValue(
        label,
        availableWidth,
      );
      const markCount = index === 0 ? "" : index + 1;
      const hidden = typeof lineValue !== "number";

      return {
        id: markConfig.propertyName,
        name: markConfig.propertyName,
        width: hidden ? 0 : 1,
        dashStyle: markConfig.style?.dashStyle ?? "ShortDash",
        color: hidden
          ? "transparent"
          : (markConfig.style?.lineColor ?? "#ff4d4f"),
        value:
          markConfig.style?.valueType === "string"
            ? Number(lineValue)
            : +lineValue,
        formattedValue,
        zIndex: 3,
        label: {
          text: `${markConfig.label ? `${markConfig.label}${markCount}:` : ""} ${formattedValue}`,
          align: markConfig.align,
          verticalAlign: markConfig.verticalAlign,
          ...markConfig.position,
          rotation: vertical ? 270 : undefined,
          useHTML: true,
          style: {
            color: hidden
              ? "transparent"
              : (markConfig.style?.labelColor ?? "#ff4d4f"),
          },
        },
      };
    };

    return Object.entries(MARKS_CONFIG)
      .filter(([markName]) =>
        plotLines.includes(MARKS_CONFIG[markName].propertyName),
      )
      .flatMap(([markName, config]) => {
        const values = Array.isArray(data[markName])
          ? data[markName]
          : [data[markName]];

        return values.map((value, index) =>
          createPlotLine(config, value, index),
        );
      });
  },
  /**
   * Generate plot line definitions of the sigmas
   *
   * @param {object} data
   * @param {boolean} vertical
   * @param {array} plotLines
   * @returns {array}
   */
  getSigmaLines: (
    data,
    vertical = false,
    plotLines = ["1s", "2s", "3s", "6s"],
  ) => {
    const marksDef = [];
    plotLines.forEach((plotLine) => {
      marksDef.push({
        value: data.mean - data.stdev * +plotLine.replace("s", ""),
        name: `-${plotLine}`,
        label: `-${plotLine}`,
        align: "right",
        verticalAlign: "top",
        propertyName: plotLine,
        x: vertical ? -4 : undefined,
        y: vertical ? 10 : -4,
      });
      marksDef.push({
        value: data.mean + data.stdev * +plotLine.replace("s", ""),
        name: plotLine,
        label: plotLine,
        align: "left",
        verticalAlign: vertical ? "bottom" : "top",
        propertyName: plotLine,
        x: vertical ? -5 : 5,
        y: vertical ? -10 : -4,
      });
    });

    return marksDef.map((markObj) => {
      const formattedValue = Helper.descriptiveFormatting(markObj.value);
      return {
        name: markObj.propertyName,
        width: 0,
        dashStyle: "dash",
        color: "transparent",
        value: markObj.value,
        formattedValue: formattedValue,
        zIndex: 3,
        label: {
          text: markObj.label,
          align: markObj.align,
          verticalAlign: markObj.verticalAlign,
          y: markObj.y ?? undefined,
          x: markObj.x ?? undefined,
          rotation: vertical ? 270 : undefined,
          style: {
            color: "transparent",
          },
        },
      };
    });
  },
  /**
   * Generates plot bands configuration for charts based on data and specified parameters
   *
   * @param {array} zoneInfo
   * @param {array} zoneLabels
   * @returns {array} plotBands
   */
  getPlotBands: (zoneInfo, zoneLabels) => {
    let plotBands = [];
    zoneLabels.forEach((label, i) => {
      const from = zoneInfo[i - 1]?.[0] ?? 0;
      const to = zoneInfo[i]?.[0] ?? 0;
      plotBands.push({
        color: "transparent",
        from: from,
        to: to,
        label: {
          text: label,
          verticalAlign: "bottom",
          y: -20,
          rotation: 0,
          style: {
            fontSize: "9px",
          },
        },
      });
    });

    return plotBands;
  },
  /**
   * Truncate plotlines label to avoid overflow if area is not enough
   * Applied only to all vertical plotlines
   *
   * @param {object} chart
   */
  setPlotLinesLabel: (chart) => {
    const availableHeight = chart.plotHeight - 10;
    const plotLines = chart.xAxis[0].plotLinesAndBands.filter(
      (plotLine) =>
        plotLine.options.from === undefined &&
        plotLine.options.to === undefined,
    );
    plotLines.forEach((plotLine) => {
      if (plotLine.label && plotLine.options.label) {
        const label = ChartHelper.formatPlotLineValue(
          plotLine.options.label.text ?? "",
          availableHeight,
        );
        plotLine.label.attr({
          text: label,
        });
      }
    });
  },
  /**
   * Convert hex color to rgba
   *
   * @param {string} hex
   * @param {int|float} opacity
   * @returns
   */
  hexToRGBA: (hex, opacity) => {
    return (
      "rgba(" +
      (hex = hex.replace("#", ""))
        .match(new RegExp("(.{" + hex.length / 3 + "})", "g"))
        .map(function (l) {
          return parseInt(hex.length % 2 ? l + l : l, 16);
        })
        .concat(isFinite(opacity) ? opacity : 1)
        .join(",") +
      ")"
    );
  },
  /**
   * Append test data to the chart's user options
   *
   * @param {object} data The full data of the test
   * @returns {object} testData The needed test data
   */
  setTestDataToUserOptions: (data) => {
    const testProps = [
      "lo_lim",
      "hi_lim",
      "robust_hi_limit",
      "robust_lo_limit",
      "min_result",
      "max_result",
      "test_unit",
    ];
    const testData = {};
    testProps.forEach((prop) => {
      testData[prop] = data[prop];
    });

    return testData;
  },
  /**
   * Get wafer id options
   *
   * @param {object} data
   * @returns {array} options
   */
  getWaferIdOptions: (data) => {
    let options = [];
    if (data.composite?.qmap_header) {
      Object.keys(data.composite.qmap_header).forEach((dsk) => {
        options.push({
          label: `${data.composite.qmap_header[dsk].file_name} - ${data.composite.qmap_header[dsk].wafer_id}`,
          value: dsk,
        });
      });
    }

    return options;
  },
  /**
   * Set wafer rotation and flip
   *
   * @param {string} rotation
   * @param {string} flip
   * @param {object} options
   */
  setWaferOrientation: (rotation, flip, options) => {
    // set default axis settings to reset orientation
    options.chart.inverted = false;
    options.xAxis.opposite = false;
    options.xAxis.reversed = false;
    options.yAxis.opposite = false;
    options.yAxis.reversed = false;
    switch (true) {
      case rotation === "90" && flip === "none":
        options.chart.inverted = true;
        options.xAxis.reversed = true;
        options.yAxis.opposite = true;
        break;
      case rotation === "180" && flip === "none":
        options.xAxis.opposite = true;
        options.xAxis.reversed = true;
        options.yAxis.opposite = true;
        options.yAxis.reversed = true;
        break;
      case rotation === "270" && flip === "none":
        options.chart.inverted = true;
        options.xAxis.opposite = true;
        options.xAxis.reversed = false;
        options.yAxis.reversed = true;
        break;
      case rotation === "0" && flip === "x":
        options.xAxis.reversed = true;
        options.yAxis.opposite = true;
        break;
      case rotation === "90" && flip === "x":
        options.chart.inverted = true;
        options.xAxis.reversed = false;
        break;
      case rotation === "180" && flip === "x":
        options.xAxis.opposite = true;
        options.yAxis.reversed = true;
        break;
      case rotation === "270" && flip === "x":
        options.chart.inverted = true;
        options.xAxis.opposite = true;
        options.xAxis.reversed = true;
        options.yAxis.opposite = true;
        options.yAxis.reversed = true;
        break;
      case rotation === "0" && flip === "y":
        options.xAxis.opposite = true;
        options.yAxis.reversed = true;
        break;
      case rotation === "90" && flip === "y":
        options.chart.inverted = true;
        options.xAxis.opposite = true;
        options.xAxis.reversed = true;
        options.yAxis.opposite = true;
        options.yAxis.reversed = true;
        break;
      case rotation === "180" && flip === "y":
        options.xAxis.reversed = true;
        options.yAxis.opposite = true;
        break;
      case rotation === "270" && flip === "y":
        options.chart.inverted = true;
        options.xAxis.reversed = false;
        break;
    }
  },
  /**
   * Generate highcharts chart image data from rendered chart
   *
   * @param {object} chart
   * @param {function} callback - function to be executed when the image has been loaded and image data has been generated
   * @param {*} callbackParams
   */
  getHighchartsImageData: (chart, callback, callbackParams) => {
    if (ChartHelper.isHighchartsChart(chart)) {
      var render_width = chart.chartWidth;
      var render_height = chart.chartHeight;
      if (
        typeof chart.legend !== "undefined" &&
        typeof chart.legend.legendHeight === "number"
      ) {
        render_height += chart.legend.legendHeight;
      }

      // Get the chart's SVG code
      var svg = chart.getSVG({
        exporting: {
          sourceWidth: render_width,
          sourceHeight: render_height,
        },
        legend: {
          navigation: {
            enabled: false,
          },
        },
      });

      // Create a canvas
      var canvas = document.createElement("canvas");
      canvas.height = render_height;
      canvas.width = render_width;

      // Create an image and draw the SVG onto the canvas
      var image = new Image();
      image.onload = function () {
        canvas
          .getContext("2d")
          .drawImage(this, 0, 0, render_width, render_height);
        var data = canvas.toDataURL("image/png");
        callback(data, callbackParams);
      };
      image.src =
        "data:image/svg+xml;base64," +
        window.btoa(
          svg.replace(/[\u00A0-\u2666]/g, function (c) {
            return "&#" + c.charCodeAt(0) + ";";
          }),
        );
    }
  },
  /**
   * Display stats info at the bottom of the chart
   *
   * @param {object} chart
   * @param {object} stats
   * @param {object} data
   * @param {object} options
   * @param {object} customData
   * @param {boolean} shouldRedraw
   */
  renderStatsInfoToChart: (
    chart,
    stats,
    data,
    options,
    customData,
    shouldRedraw = true,
  ) => {
    const defaultSpacingBottom = 15;
    const statsInfo = getStatsInfo(chart, stats, data);
    if (statsInfo) {
      // remove displayed stats info if exists
      if (
        customData.statsInfoElement &&
        customData.shouldRemoveStatsInfoElement !== false
      ) {
        customData.statsInfoElement.forEach((element) => {
          element.remove();
        });
        // resize chart to its original height if not in full screen mode
        if (!customData.isFullScreen) {
          options.chart.height =
            customData.origChartHeight - customData.statsInfoHeight;
          chart.setSize(chart.chartWidth, options.chart.height, false);
        }
      }
      const statsInfoElement = chart.renderer.g("stats-info").add();
      const statsInfoText = chart.renderer
        .text(`${statsInfo}`, 0, 0, true)
        .css({
          fontSize: "12px",
          width: `${getStatsInfoWidth(chart)}px`,
        })
        .add(statsInfoElement);
      const statsInfoHeight = statsInfoText.element.clientHeight ?? 0;
      const extraSpacingBottom = customData.isFullScreen
        ? defaultSpacingBottom
        : 0;

      // save chart original height if in full screen mode, save stats info height otherwise
      if (customData.isFullScreen) {
        customData.origChartHeight = customData.fullscreen.origHeight;
      } else {
        customData.statsInfoHeight = statsInfoHeight;
      }

      // add space for stats info display at the bottom of the chart
      options.chart.spacingBottom =
        statsInfoHeight + defaultSpacingBottom + extraSpacingBottom;
      chart.update(
        {
          chart: { spacingBottom: options.chart.spacingBottom },
        },
        false,
      );

      options.chart.height = chart.chartHeight + statsInfoHeight;
      chart.setSize(chart.chartWidth, options.chart.height, false);

      // reposition stats info element
      statsInfoText.attr({
        x: chart.plotLeft,
        y: options.chart.height - statsInfoHeight - extraSpacingBottom,
      });

      // store rendered stats info element to be used when removing the displayed element to avoid duplicate rendering
      if (customData.shouldRemoveStatsInfoElement !== false) {
        customData.statsInfoElement = chart.container.querySelectorAll(
          ".highcharts-stats-info",
        );
      }
    }
    customData.shouldRemoveStatsInfoElement = true;

    if (shouldRedraw) {
      chart.redraw();
    }
  },
  /**
   * Display wafer info at the bottom of the chart
   *
   * @param {object} chart
   * @param {string} waferInfo
   * @param {object} options
   * @param {object} customData
   */
  renderWaferInfoToChart: (chart, waferInfo, options, customData) => {
    const defaultSpacingBottom = 20;
    customData.waferInfo = waferInfo;
    if (waferInfo) {
      // remove displayed wafer info if exists
      if (
        customData.waferInfoElement &&
        customData.shouldRemoveWaferInfoElement !== false
      ) {
        removeWaferInfo(chart, options, customData);
      }
      const waferInfoElement = chart.renderer.g("wafer-info").add();
      const waferInfoText = chart.renderer
        .text(`${waferInfo}`, 0, 0, true)
        .css({
          fontSize: "12px",
          width: `${getWaferInfoWidth(chart)}px`,
        })
        .add(waferInfoElement);
      const waferInfoHeight = waferInfoText.element.clientHeight ?? 0;

      options.chart.spacingBottom = waferInfoHeight + defaultSpacingBottom;

      if (!customData.isChartNavigated) {
        options.chart.height =
          (!customData.isFullScreen && customData.fullscreen.origHeight !== 0
            ? customData.fullscreen.origHeight -
              (customData.fullscreen.spacingBottom ?? 0)
            : chart.chartHeight) +
          options.chart.spacingBottom -
          10;
        customData.initialChartHeight = options.chart.height;
        customData.isChartNavigated = true;
      } else {
        options.chart.height = customData.initialChartHeight;
      }

      // save chart original height if in full screen mode, save wafer info height otherwise
      if (customData.isFullScreen) {
        customData.origChartHeight =
          customData.fullscreen.origHeight ?? options.chart.height;
      } else {
        customData.waferInfoHeight = waferInfoHeight;
      }

      chart.renderTo.style.height = `${options.chart.height}px`;
      chart.setSize(chart.chartWidth, options.chart.height, false);

      // reposition wafer info element
      waferInfoText.attr({
        x: chart.plotLeft,
        y: options.chart.height - waferInfoHeight,
      });

      // store rendered wafer info element to be used when removing the displayed element to avoid duplicate rendering
      if (customData.shouldRemoveWaferInfoElement !== false) {
        customData.waferInfoElement = chart.container.querySelectorAll(
          ".highcharts-wafer-info",
        );
      }

      chart.update(options);
    }
    customData.shouldRemoveWaferInfoElement = true;
  },
  /**
   * Remove rendered wafer info and space allocated
   *
   * @param {object} chart
   * @param {object} options
   * @param {object} customData
   * @param {boolean} redraw
   */
  removeWaferInfo: (chart, options, customData, redraw = false) => {
    removeWaferInfo(chart, options, customData, redraw);
    delete customData.waferInfo;
  },
  /**
   * Highlight categories
   *
   * @param {object} chart
   * @param {object} series
   * @param {array} categories
   * @param {array} highlightCategories
   */
  highlightCategories: (
    chart,
    series,
    categories = [],
    highlightCategories = [],
  ) => {
    if (highlightCategories.length > 0 && categories.length > 0) {
      const pointMarker = series?.options?.marker;
      const baseRadius = pointMarker?.radius;
      const radiusPlus = pointMarker?.states?.hover?.radiusPlus;
      const lineWidth = series?.options?.lineWidth;
      const pointWidth =
        series.points?.[0]?.pointWidth ?? // For block-based charts
        (baseRadius + radiusPlus || lineWidth); // For line-based charts
      const plotWidth = pointWidth < 20 ? pointWidth : pointWidth + 20;
      if (plotWidth > 0) {
        highlightCategories.forEach((category) => {
          const categoryIndex = categories.indexOf(category);
          if (categoryIndex !== -1) {
            const id = `highlight_category_${category}`;
            chart.xAxis[0].removePlotLine(id);
            chart.xAxis[0].addPlotLine({
              value: categoryIndex,
              color: "yellow",
              width: plotWidth,
              id: id,
            });
          }
        });
      }
    }
  },
  /**
   * Enter/exit chart in full screen
   *
   * @param {object} chart
   * @param {object} customData - chart custom data
   * @param {object} fullScreenHandle
   */
  toggleFullScreen: (chart, customData, fullScreenHandle) => {
    toggleFullScreen(chart, customData, fullScreenHandle);
  },
  /**
   * Update exporting menu
   *
   * @param {object} chart
   * @oaram {function} setIsChartOptionsOpen
   * @param {function} setCurrentChart
   * @param {object} customData
   * @param {object} fullScreenHandle
   */
  updateExportingMenu: (
    chart,
    setIsChartOptionsOpen,
    setCurrentChart,
    customData,
    fullScreenHandle,
  ) => {
    chart.exporting.update(
      generateHighchartsMenu(
        setIsChartOptionsOpen,
        setCurrentChart,
        customData,
        fullScreenHandle,
      ),
    );
  },
  /**
   * Update full screen view exporting menu label
   *
   * @param {object} chart
   * @param {boolean} isFullScreen
   */
  updateFullScreenViewLabel: (chart, isFullScreen) => {
    const label = isFullScreen
      ? "Exit from full screen"
      : "View in full screen";
    chart.exporting.update({
      menuItemDefinitions: {
        fullScreenView: {
          text: label,
        },
      },
    });
  },
  /**
   * Generate chart title based on provided data
   *
   * @param {string} chartTitle
   * @param {object} data
   * @returns {string} newChartTitle
   */
  generateChartTitle: (chartTitle, data) => {
    let newChartTitle = chartTitle;
    Object.keys(data).forEach((key) => {
      if (data[key]) {
        const regex = new RegExp(`{${key}}`, "g");
        newChartTitle = newChartTitle.replace(regex, data[key]);
      }
    });

    return newChartTitle;
  },
  /**
   * Store chart key to chart keys global state
   *
   * @param {string} chartKey
   * @param {object} chartKeys
   * @param {string} pageKey
   * @param {string} componentName
   */
  storeChartKey: (chartKey, chartKeys, pageKey, componentName) => {
    if (chartKeys[pageKey] === undefined) {
      chartKeys[pageKey] = {};
    }
    if (Array.isArray(chartKeys[pageKey][componentName])) {
      chartKeys[pageKey][componentName].push(chartKey);
      chartKeys[pageKey][componentName] = uniq(
        chartKeys[pageKey][componentName],
      );
    } else {
      chartKeys[pageKey][componentName] = [chartKey];
    }
  },
  /**
   * Check if chart is boosted
   *
   * @param {object} chart
   * @returns {boolean}
   */
  isChartBoosted: (chart) => {
    return chart.boosted || chart.series.every((s) => s.boosted === true);
  },
  /*
   * Generate a histogram chart data format given the results and parameters
   *
   * @param {object} data
   * @param {object} param
   * @param {string} type
   * @returns {object}
   */
  generateHistogramData: (data, param, type = "bar") => {
    const groupedData = getHistogramGroupedData(data);
    const [min, max, minLimit, maxLimit, hasMinLimit, hasMaxLimit] =
      getHistogramScalingData(groupedData, param);
    const [slotSize, minX, maxX] = getHistogramSlotSizeAndMinMaxData(
      min,
      max,
      minLimit,
      maxLimit,
      hasMinLimit,
      hasMaxLimit,
      param,
    );
    const slotCount = getHistogramSlotCount(min, max, minX, maxX, slotSize);

    param.slot_count = slotCount;
    param.slot_size = slotSize;
    param.min_x = minX;
    param.max_x = maxX;

    const xLabels = Array.from(
      { length: slotCount + 1 },
      (_, i) => minX + i * slotSize,
    );
    const [yData, legend, dataPointsCount] = calculateHistogramData(
      groupedData,
      param,
      type,
    );

    if (type !== "bar") {
      if ((yData[0] ?? 0) > 0) {
        yData.unshift(0);
        xLabels.unshift(xLabels[0] - slotSize);
      }
      if ((yData.at(-1) ?? 0) > 0) {
        yData.push(0);
        xLabels.push(xLabels.at(-1) + slotSize);
      }
    }

    const xyData = yData.map((frequency) => {
      return frequency.map((y, i) => {
        return [xLabels[i], y];
      });
    });

    const chartData = {
      x_categories: xLabels,
      data: xyData,
      legend,
      slot_size: slotSize,
      data_points_count: dataPointsCount,
    };

    return chartData;
  },
  /**
   * Get chart options related to histogram data calculation
   *
   * @param {object} settings
   * @param {object} chartData
   * @param {object} prerenderData
   * @param {object} userChartOptions
   * @param {string} type
   * @returns {object}
   */
  getHistogramOptions: (
    settings,
    chartData,
    prerenderData,
    userChartOptions = {},
    type = "bar",
  ) => {
    const histogramConfig = {
      bin_count: userChartOptions.bin_count,
      bin_width: userChartOptions.bin_width,
      x_min_range:
        userChartOptions.x_min_range ??
        Math.min(chartData.min_result, chartData.lo_limit ?? chartData.lo_lim),
      x_max_range:
        userChartOptions.x_max_range ??
        Math.max(chartData.max_result, chartData.hi_limit ?? chartData.hi_lim),
      y_as_pct: prerenderData.histogram_y_axis_as_percent,
    };
    const histogramData = ChartHelper.generateHistogramData(
      chartData,
      histogramConfig,
      type,
    );
    const series = getHistogramDataSeries(
      histogramData.data,
      settings,
      chartData.legend ?? chartData.chart_legend,
      chartData.series_colors,
      type,
    );

    return {
      series: series,
      x_categories: histogramData.x_categories,
    };
  },
  /**
   * Render only the plot area in chart (i.e., no title, legend, axes, labels, credits, or tooltip)
   *
   * @param {object} chartOptions
   */
  setPlotAreaOnlyOptions: (chartOptions) => {
    merge(chartOptions, {
      chart: {
        ...(chartOptions.chart ?? {}),
        animation: false,
        height: "100%",
        margin: [5, 5, 5, 5],
        marginTop: 5,
        marginBottom: 5,
        marginLeft: 5,
        marginRight: 5,
        plotBorderWidth: 1,
      },
      title: {
        text: null,
      },
      subtitle: {
        text: null,
      },
      legend: {
        enabled: false,
      },
      xAxis: chartOptions.xAxis.map((xAxis) => {
        return {
          ...xAxis,
          visible: false,
        };
      }),
      yAxis: chartOptions.yAxis.map((yAxis) => {
        return {
          ...yAxis,
          visible: false,
        };
      }),
      credits: {
        enabled: false,
      },
      tooltip: {
        enabled: false,
      },
      exporting: {
        enabled: false,
      },
    });
  },
  /**
   * Check if object is highcharts chart or not
   *
   * @param {object} obj
   * @returns {boolean}
   */
  isHighchartsChart: (obj) => {
    return obj && obj.renderTo;
  },
};

export default ChartHelper;
