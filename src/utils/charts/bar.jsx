"use client";

import { App, Empty, notification } from "antd";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/modules/pareto";
import "highcharts/modules/no-data-to-display";
import "highcharts/modules/exporting";
import "highcharts/modules/offline-exporting";
import "highcharts/modules/accessibility";
import "highcharts/modules/boost";
import React, { useEffect, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { merge } from "lodash";
import Api from "../api";
import Helper from "../helper";
import { useBoundStore } from "../../store/store";
import { QueryKeys } from "../query_keys";
import { useChartDefaultOptions } from "../../hooks/useChartDefaultOptions";
import ChartHelper from "./chart_helper";
import ChartLoading from "./loading";

/**
 * Bar chart
 *
 * @param {React.Ref} chartRef
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @param {function} setIsChartBoosted
 * @param {function} setHasChartData
 * @param {object} fullScreenHandle
 * @param {object} prerenderData
 * @param {React.Ref} ref
 * @returns {JSX.Element}
 */
export default React.forwardRef(function Bar(
  {
    chartRef,
    component,
    filters,
    pageKey,
    chartKey,
    chartCustomData,
    setIsChartBoosted,
    setHasChartData,
    fullScreenHandle,
    prerenderData = {},
  },
  ref,
) {
  const [chartOptions, setChartOptions] = useState();
  const reloadChartFilters = useBoundStore((state) => state.reloadChartFilters);
  const chartComponentRefs = useBoundStore((state) => state.chartComponentRefs);
  const retainedChartOptions = useBoundStore(
    (state) => state.retainedChartOptions,
  );
  const setIsChartOptionsOpen = useBoundStore(
    (state) => state.setIsChartOptionsOpen,
  );
  const setCurrentChart = useBoundStore((state) => state.setCurrentChart);
  const { message } = App.useApp();
  const [notificationApi, contextHolder] = notification.useNotification();
  const queryClient = useQueryClient();
  const params = component.props.params;
  const settings = component.props.settings;
  const chartType = component.name;

  const options = merge(ChartHelper.getChartDefaultSettings(), {
    chart: {
      renderTo: "container",
      type: "bar",
      width: settings.width ? settings.width : null,
      height: settings.height ? settings.height : "auto",
      marginRight: 80,
      zoomType: "xy",
      events: {
        render: (event) => {
          const chart = event.target;
          if (typeof setIsChartBoosted === "function") {
            setIsChartBoosted(chart.boosted);
          }
        },
      },
    },
    title: {
      text: settings.title,
    },
    tooltip: {
      shared: true,
    },
    xAxis: [
      {
        title: {
          text: settings.x.title ?? "",
        },
        labels: {
          autoRotation: false,
          rotation: 0,
          style: {
            width: "100px",
            whiteSpace: "nowrap",
            textOverflow: "ellipsis",
            overflow: "hidden",
            fontSize: "12px",
          },
        },
      },
    ],
    yAxis: [
      {
        title: {
          text: settings.y.title ?? "",
        },
        min: settings.y.min ?? null,
        max: settings.y.max ?? null,
        softMax: settings.y.soft_max ?? null,
      },
    ],
    series: [
      {
        name: settings.title ?? "",
        data: [],
      },
    ],
    plotOptions: {
      series: {
        custom: {
          settings: settings,
        },
      },
    },
    yhDefinedConfig: {
      name: Helper.titlelize(chartType),
      chartType: chartType,
      chartKey: chartKey,
      default: settings,
    },
  });

  useEffect(() => {
    if (typeof Highcharts !== "object") {
      message.warning("Highcharts not loaded.", 10);
    }
    chartComponentRefs[chartKey] = ref;

    return () => {
      queryClient.removeQueries({
        queryKey: QueryKeys.chart(pageKey, chartKey),
      });
    };
  }, []);

  React.useImperativeHandle(ref, () => ({
    reloadChart: () => {
      if (reloadChartFilters[chartKey].chart_bin_type !== undefined) {
        filters[pageKey].bin_type = reloadChartFilters[chartKey].chart_bin_type;
        delete reloadChartFilters[chartKey].chart_bin_type;
        chartDataQuery.refetch();
      }
    },
  }));

  /**
   * Fetch chart data
   *
   * @param {object} context
   * @param {AbortSignal} context.signal
   * @returns {Promise} response
   */
  const fetchChartData = async ({ signal }) => {
    chartRef.current?.chart?.hideNoData();
    chartRef.current?.chart?.showLoading();

    const allFilters = {
      ...filters[pageKey],
      tNum: prerenderData.test_number,
      ...prerenderData,
    };
    const url = Helper.parseUrlEndpoint(params.url_endpoint, allFilters);
    const requestParams = Helper.filterObjectByKeys(
      allFilters,
      Object.keys(params.body_params),
    );

    const response = await Api.fetchData(
      url,
      params.method,
      {
        ...requestParams,
      },
      params.body_params,
      signal,
    );

    return response;
  };

  /**
   * Fetch the default chart options of this chart type
   */
  const defaultChartOptions = useChartDefaultOptions(chartType);

  /**
   * Query to fetch chart data
   */
  const chartDataQuery = useQuery({
    queryKey: QueryKeys.chart(pageKey, chartKey),
    queryFn: fetchChartData,
    enabled: defaultChartOptions.isSuccess,
  });

  useEffect(() => {
    if (chartDataQuery.isSuccess) {
      const response = chartDataQuery.data;
      if (response.success) {
        setHasChartData(
          Array.isArray(response.data.y_data) &&
            response.data.y_data.length > 0,
        );
        options.xAxis[0].categories = response.data.x_categories;
        options.series = [
          {
            name: settings?.bar?.title ?? "",
            data: response.data?.y_data ?? [],
            tooltip: {
              pointFormatter: function () {
                return `${settings.y.title}: <b>${Helper.numberFormat(this.y, 0)}</b>`;
              },
            },
          },
        ];

        const chartOptions = defaultChartOptions.data?.data?.value
          ? merge(
              options,
              JSON.parse(defaultChartOptions.data.data.value),
              retainedChartOptions[chartKey] ?? {},
            )
          : options;
        setChartOptions(chartOptions);
      } else {
        notificationApi.warning({
          message: component.display_name,
          description: response.message,
        });
      }
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.dataUpdatedAt]);

  useEffect(() => {
    if (chartDataQuery.isError) {
      notificationApi.error({
        message: component.display_name,
        description: chartDataQuery.error,
      });
      chartRef.current?.chart?.hideLoading();
    }
  }, [chartDataQuery.isError]);

  /**
   * A callback function for the created chart
   *
   * @param {object} chart
   */
  const onChartLoaded = (chart) => {
    ChartHelper.updateExportingMenu(
      chart,
      setIsChartOptionsOpen,
      setCurrentChart,
      chartCustomData[chartKey],
      fullScreenHandle,
    );
  };

  return (
    <div>
      {contextHolder}
      {typeof Highcharts === "object" ? (
        chartOptions ? (
          <HighchartsReact
            ref={chartRef}
            highcharts={Highcharts}
            options={chartOptions}
            callback={onChartLoaded}
            containerProps={{
              style: {
                // needed to fix highcharts react wrapper bug where chart does not display the original size after exiting full screen
                height: chartRef.current
                  ? chartRef.current.chart.chartHeight
                  : settings.height,
              },
            }}
          />
        ) : (
          <ChartLoading height={settings.height} />
        )
      ) : (
        <Empty description="Highcharts not loaded"></Empty>
      )}
    </div>
  );
});
