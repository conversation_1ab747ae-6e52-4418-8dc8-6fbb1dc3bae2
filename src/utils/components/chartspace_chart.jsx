import { useEffect, useRef, useState } from "react";
import { useFullScreenHandle } from "react-full-screen";
import dynamic from "next/dynamic";
import { useBoundStore } from "../../../src/store/store";
const BarHistogram = dynamic(() => import("../charts/bar_histogram"), {
  ssr: false,
});
const CurveHistogram = dynamic(() => import("../charts/curve_histogram"), {
  ssr: false,
});
const NormalProbability = dynamic(
  () => import("../charts/normal_probability"),
  {
    ssr: false,
  },
);
const WaferMap = dynamic(() => import("../charts/wafer_map"), {
  ssr: false,
});
const Scatter = dynamic(() => import("../charts/scatter"), {
  ssr: false,
});
const BoxPlot = dynamic(() => import("../charts/box_plot"), {
  ssr: false,
});
import ChartWrapper from "../charts/chart_wrapper";
import ChartHelper from "../charts/chart_helper";

/**
 * Chartspace chart component
 *
 * @param {string} chartType
 * @param {string} chartKey
 * @param {object} chartCustomData
 * @param {object} filters
 * @param {string} pageKey
 * @param {object} component
 * @param {object} chartFilters
 * @param {function} testStatsInfoHandler
 * @param {object} prerenderData
 * @param {boolean} showBoostWarning
 * @returns {JSX.Element}
 */
export const ChartspaceChart = ({
  chartType,
  chartKey,
  chartCustomData,
  filters,
  pageKey,
  component,
  chartFilters,
  testStatsInfoHandler,
  prerenderData = {},
  showBoostWarning = true,
}) => {
  const [chartComponent, setChartComponent] = useState();
  const [hasChartData, setHasChartData] = useState(true);
  const [isChartBoosted, setIsChartBoosted] = useState(false);
  const chartRef = useRef();
  const chartComponentRef = useRef();
  const fullScreenHandle = useFullScreenHandle();
  const chartKeys = useBoundStore((state) => state.chartKeys);

  // initialize chart custom data
  ChartHelper.initChartCustomData(chartCustomData, chartKey, fullScreenHandle);

  useEffect(() => {
    ChartHelper.storeChartKey(chartKey, chartKeys, pageKey, component.name);
    renderChart();
  }, []);

  /**
   * Set chart component to be rendered on page
   *
   */
  const renderChart = () => {
    let chart;

    switch (chartType) {
      case "bar_histogram":
        chart = (
          <BarHistogram
            chartRef={chartRef}
            component={component}
            filters={filters}
            pageKey={pageKey}
            chartKey={chartKey}
            chartCustomData={chartCustomData}
            chartFilters={chartFilters}
            setHasChartData={setHasChartData}
            fullScreenHandle={fullScreenHandle}
            prerenderData={prerenderData}
          />
        );
        break;
      case "curve_histogram":
        chart = (
          <CurveHistogram
            chartRef={chartRef}
            component={component}
            filters={filters}
            pageKey={pageKey}
            chartKey={chartKey}
            chartCustomData={chartCustomData}
            chartFilters={chartFilters}
            setHasChartData={setHasChartData}
            fullScreenHandle={fullScreenHandle}
            prerenderData={prerenderData}
          />
        );
        break;
      case "qq_plot":
        chart = (
          <NormalProbability
            chartRef={chartRef}
            component={component}
            filters={filters}
            pageKey={pageKey}
            chartKey={chartKey}
            chartCustomData={chartCustomData}
            setIsChartBoosted={setIsChartBoosted}
            chartFilters={chartFilters}
            setHasChartData={setHasChartData}
            fullScreenHandle={fullScreenHandle}
            prerenderData={prerenderData}
          />
        );
        break;
      case "scatter":
        chart = (
          <Scatter
            ref={chartComponentRef}
            chartRef={chartRef}
            component={component}
            filters={filters}
            pageKey={pageKey}
            chartKey={chartKey}
            chartCustomData={chartCustomData}
            chartFilters={chartFilters}
            testStatsInfoHandler={testStatsInfoHandler}
            setHasChartData={setHasChartData}
            setIsChartBoosted={setIsChartBoosted}
            fullScreenHandle={fullScreenHandle}
            prerenderData={prerenderData}
          />
        );
        break;
      case "boxplot":
        chart = (
          <BoxPlot
            chartRef={chartRef}
            component={component}
            filters={filters}
            pageKey={pageKey}
            chartKey={chartKey}
            chartCustomData={chartCustomData}
            chartFilters={chartFilters}
            setHasChartData={setHasChartData}
            fullScreenHandle={fullScreenHandle}
            prerenderData={prerenderData}
          />
        );
        break;
      case "wafer":
        chart = (
          <WaferMap
            ref={chartComponentRef}
            chartRef={chartRef}
            component={component}
            filters={filters}
            pageKey={pageKey}
            chartKey={chartKey}
            chartCustomData={chartCustomData}
            chartFilters={chartFilters}
            chartSettings={{
              decimalPlaces: 3,
            }}
            setHasChartData={setHasChartData}
            setIsChartBoosted={setIsChartBoosted}
            fullScreenHandle={fullScreenHandle}
            prerenderData={prerenderData}
          />
        );
        break;
    }
    setChartComponent(chart);
  };

  return (
    <ChartWrapper
      key={chartKey}
      chartComponent={chartComponent}
      chartRef={chartRef}
      component={component}
      pageKey={pageKey}
      waferIdOptions={[]}
      chartCustomData={chartCustomData}
      chartKey={chartKey}
      displayChartControls={false}
      hasChartData={hasChartData}
      isChartBoosted={isChartBoosted}
      showBoostWarning={showBoostWarning}
      fullScreenHandle={fullScreenHandle}
    />
  );
};
