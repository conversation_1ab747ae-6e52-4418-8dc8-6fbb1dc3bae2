import {
  // SettingOutlined,
  InfoCircleTwoTone,
  DeleteOutlined,
} from "@ant-design/icons";
import {
  App,
  Button,
  Checkbox,
  Col,
  Divider,
  Form,
  Input,
  // InputNumber,
  Modal,
  Row,
  Select,
  Space,
  Tooltip,
  Typography,
} from "antd";
import React, { useEffect, useState } from "react";
import { useBoundStore } from "../../store/store";
import Api from "../api";
import Helper from "../helper";
import ChartspaceChartOptionsForm from "../forms/chartspace_chart_options_form";
import { ComponentNameMapper } from "../grid/component_name_mapper";
import GridHelper from "../grid/grid_helper";
import { OptionsList } from "../forms/options_list";
import { ChartspaceChart } from "./chartspace_chart";

const { Text, Title } = Typography;

const initialChartspaceChartOptions = {
  chart: { label: "Bar Histogram", value: "bar_histogram" },
  display_chart_size: 2,
  stats_type: "rp",
  cs_chart_range: "test_limits",
  show_limits: true,
  pin_chart_display: { value: "per_pin", label: "Per Pin (Individual Charts)" },
};

/**
 * Get all tests table selection store key based on analysis type
 *
 * @param {object} selectionStoreKeyByType
 * @param {string} analysisType
 * @returns {string} selectionStoreKey
 */
const getSelectionStoreKey = (selectionStoreKeyByType, analysisType) => {
  const selectionStoreKey = selectionStoreKeyByType[analysisType] ?? "";

  return selectionStoreKey;
};

/**
 * Chart space that displays test charts
 *
 * @param {object} component
 * @param {object} filters
 * @param {string} pageKey
 * @param {object} chartCustomData
 * @param {function} testStatsInfoHandler
 * @returns {JSX.Element}
 */
export default function Chartspace({
  component,
  filters,
  pageKey,
  chartCustomData,
  testStatsInfoHandler,
}) {
  const [chartspaceCharts, setChartspaceCharts] = useState([]);
  const [chartsPerRow, setChartsPerRow] = useState([]);
  const [isReplaceCharts, setIsReplaceCharts] = useState(true);
  const [hiddenChartspaceIndexes, setHiddenChartspaceIndexes] = useState([]);
  const [componentBlueprints, setComponentBlueprints] = useState({});
  const [isChartspaceChartOptionsOpen, setIsChartspaceChartOptionsOpen] =
    useState(false);
  const [isChartspaceDisabled, setIsChartspaceDisabled] = useState(true);
  const gridSelectionData = useBoundStore((state) => state.gridSelectionData);
  const detailGridComponents = useBoundStore(
    (state) => state.detailGridComponents,
  );
  const pageMeta = useBoundStore((state) => state.pageMeta);
  const [chartspaceOptionsForm] = Form.useForm();
  const [chartspaceChartOptionsForm] = Form.useForm();
  const { message } = App.useApp();
  const selectionStoreKey = getSelectionStoreKey(
    component.selection_store_key_by_type,
    pageMeta.analysis_type,
  );
  const detailGridComponentsKey =
    pageMeta.analysis_type === "multiple"
      ? `${pageKey}_${ComponentNameMapper.lot_all_tests_aggregate_table}`
      : pageMeta.analysis_type === "grouped"
        ? `${pageKey}_${ComponentNameMapper.lot_all_tests_grouped_table}`
        : `${pageKey}_${ComponentNameMapper.lot_all_tests}`;
  const chartComponentNames = {
    bar_histogram:
      pageMeta.analysis_type === "grouped"
        ? ComponentNameMapper.grouped_parametric_bar_histogram
        : ComponentNameMapper.lot_parametric_bar_histogram,
    curve_histogram:
      pageMeta.analysis_type === "grouped"
        ? ComponentNameMapper.grouped_parametric_curve_histogram
        : ComponentNameMapper.lot_parametric_curve_histogram,
    qq_plot:
      pageMeta.analysis_type === "grouped"
        ? ComponentNameMapper.grouped_parametric_qq_plot
        : ComponentNameMapper.lot_parametric_qq_plot,
    scatter: ComponentNameMapper.lot_parametric_scatter,
    boxplot:
      pageMeta.analysis_type === "grouped"
        ? ComponentNameMapper.grouped_parametric_box_plot
        : ComponentNameMapper.lot_parametric_box_plot,
    wafer:
      pageMeta.analysis_type === "single"
        ? ComponentNameMapper.lot_parametric_wafer_map_single
        : ComponentNameMapper.lot_parametric_wafer_map_mean,
  };

  useEffect(() => {
    getComponentBlueprints();
  }, []);

  useEffect(() => {
    const selectedRows = GridHelper.getSelectedRows(
      gridSelectionData,
      selectionStoreKey,
      detailGridComponents[detailGridComponentsKey],
    );
    setIsChartspaceDisabled(selectedRows.length === 0);
  }, [gridSelectionData]);

  /**
   * Get component blueprints to be used when generating chartspace charts
   */
  const getComponentBlueprints = () => {
    Api.getAnalysisTemplateComponents(
      (res) => {
        if (res.success) {
          const lotComponents = res.data.lot;
          const blueprints = lotComponents.reduce((result, lotComponent) => {
            result[lotComponent.name] = lotComponent;
            return result;
          }, {});
          setComponentBlueprints(blueprints);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      "lot",
    );
  };

  /**
   * Triggers when replace charts checkbox value changes
   *
   * @param {Event} e
   */
  const onChangeReplaceCharts = (e) => {
    setIsReplaceCharts(e.target.checked);
  };

  /**
   * Plot charts for selected tests
   *
   * @param {object} values
   */
  const plotChartspaceCharts = (values) => {
    const chartType = values.chart.value;
    if (isChartspaceChartOptionsOpen) {
      chartspaceOptionsForm.setFieldsValue(values);
      setIsChartspaceChartOptionsOpen(!isChartspaceChartOptionsOpen);
    }
    const selectedRows = GridHelper.getSelectedRows(
      gridSelectionData,
      selectionStoreKey,
      detailGridComponents[detailGridComponentsKey],
    );
    if (selectedRows.length > 0) {
      renderSelectedTestsCharts(chartType, values);
    } else {
      message.warning("Please select at least one test.", 5);
    }
  };

  /**
   * Render selected tests charts to chartspace
   *
   * @param {string} chartType
   * @param {object} chartFilters
   * @returns {AbortController} abortCtl
   */
  const renderSelectedTestsCharts = (chartType, chartFilters) => {
    let chartspaceChartsCopy;
    let chartsPerRowCopy;
    if (isReplaceCharts === true) {
      chartspaceChartsCopy = [];
      chartsPerRowCopy = [];
      setHiddenChartspaceIndexes([]);
    } else {
      chartspaceChartsCopy = Helper.cloneObject(chartspaceCharts);
      chartsPerRowCopy = Helper.cloneObject(chartsPerRow);
    }
    chartspaceChartsCopy.push({});
    chartsPerRowCopy.push(chartFilters.display_chart_size);
    setChartsPerRow(chartsPerRowCopy);

    const chartspaceIndex = chartspaceChartsCopy.length - 1;

    const selectedRows = GridHelper.getSelectedRows(
      gridSelectionData,
      selectionStoreKey,
      detailGridComponents[detailGridComponentsKey],
    );
    if (selectedRows.length > 0) {
      chartspaceChartsCopy = addSelectionToChartspaceCharts(
        chartspaceChartsCopy,
        chartspaceIndex,
        chartFilters,
        chartType,
        selectedRows,
      );
    }
    setChartspaceCharts(chartspaceChartsCopy);
  };

  /**
   * Add selected tests data to chartspace charts object
   *
   * @param {object} chartspaceChartsCopy
   * @param {int} chartspaceIndex
   * @param {object} chartFilters
   * @param {string} chartType
   * @param {array} selectedRows
   * @returns {object} chartspaceChartsCopy
   */
  const addSelectionToChartspaceCharts = (
    chartspaceChartsCopy,
    chartspaceIndex,
    chartFilters,
    chartType,
    selectedRows,
  ) => {
    let chartKey;
    let chartComponentBlueprintCopy;
    const perPin = chartFilters.per_pin;
    const chartComponentBlueprint =
      componentBlueprints[chartComponentNames[chartType]];

    let testsData = [];
    if (perPin === true && chartFilters.pin_chart_display.value !== "per_pin") {
      testsData.push({
        tnum: selectedRows.map((row) => row.tnum)[0],
        pin_index: selectedRows.map((row) => row.pin_index).join(","),
        dsk: selectedRows.map((row) => row.dsk)[0],
        title: `${chartFilters.chart.label}: ${selectedRows.map((row) => row.pin_name).join(", ")}`,
      });
    } else {
      selectedRows.forEach((row) => {
        testsData.push({
          tnum: row.tnum,
          pin_index: row.pin_index?.toString(),
          dsk: row.dsk,
          title:
            perPin === true
              ? `${chartFilters.chart.label}: ${row.pin_name}`
              : undefined,
        });
      });
    }
    if (perPin === true) {
      chartspaceChartsCopy[chartspaceIndex]["title"] =
        chartFilters.pin_chart_display.label;
    }
    chartspaceChartsCopy[chartspaceIndex]["charts"] = {};
    testsData.forEach((testData) => {
      const chartFiltersCopy = Helper.cloneObject(chartFilters);
      if (
        filters[pageKey].src_type === "dsk" &&
        typeof testData.dsk !== "undefined"
      ) {
        chartFiltersCopy.src_value = Array.isArray(testData.dsk)
          ? testData.dsk.join(",")
          : testData.dsk;
      }
      if (testData.title) {
        chartFiltersCopy.title = testData.title;
      }
      chartKey = generateChartspaceChartKey(
        chartType,
        testData.tnum,
        testData.pin_index,
        chartspaceIndex,
        chartFiltersCopy,
        perPin,
      );
      chartComponentBlueprintCopy = Helper.cloneObject(chartComponentBlueprint);
      chartComponentBlueprintCopy.props.params.body_params.test_number =
        testData.tnum;
      if (perPin === true) {
        chartComponentBlueprintCopy.props.params.body_params.per_pin = perPin;
        chartComponentBlueprintCopy.props.params.body_params.pin_index =
          testData.pin_index;
      }
      chartspaceChartsCopy[chartspaceIndex]["charts"][chartKey] = {
        key: chartKey,
        chartType: chartType,
        chartKey: chartKey,
        chartCustomData: chartCustomData,
        filters: filters,
        pageKey: pageKey,
        component: chartComponentBlueprintCopy,
        chartFilters: {
          ...chartFiltersCopy,
          pin_chart_display: chartFilters.pin_chart_display.value,
        },
        testStatsInfoHandler: testStatsInfoHandler,
      };
    });

    return chartspaceChartsCopy;
  };

  /**
   * Generate chartspace chart key based on chart type and test number
   *
   * @param {string} chartType
   * @param {string} testNum
   * @param {string} pinIndex
   * @param {int} chartspaceIndex
   * @param {object} chartFilters
   * @param {boolean} perPin
   * @returns {string} chartKey
   */
  const generateChartspaceChartKey = (
    chartType,
    testNum,
    pinIndex,
    chartspaceIndex,
    chartFilters,
    perPin,
  ) => {
    const chartKey =
      perPin === true
        ? `${chartType}_${testNum}_${pinIndex}_${chartspaceIndex}_${JSON.stringify(
            chartFilters,
          )}`
        : `${chartType}_${testNum}_${chartspaceIndex}_${JSON.stringify(
            chartFilters,
          )}`;

    return chartKey;
  };

  /**
   * Remove charts based on chartspace index
   *
   * @param {int} index
   */
  const removeChartsByChartspaceIndex = (index) => {
    let hiddenChartspaceIndexesCopy = Helper.cloneObject(
      hiddenChartspaceIndexes,
    );
    hiddenChartspaceIndexesCopy.push(index);
    setHiddenChartspaceIndexes(hiddenChartspaceIndexesCopy);
  };

  /**
   * Render chartspace divider
   *
   * @param {int} index
   * @returns {JSX.Element}
   */
  const renderDivider = (index) => {
    let isFirst = true;
    for (let i = 0; i < index; i++) {
      if (hiddenChartspaceIndexes.indexOf(i) === -1) {
        isFirst = false;
      }
    }

    return !isFirst ? <Divider className="mt-5 mb-0" /> : <></>;
  };

  return (
    <div>
      <Row>
        <Col span={24}>
          <Form
            form={chartspaceOptionsForm}
            layout="inline"
            initialValues={{
              ...initialChartspaceChartOptions,
              per_pin: component.per_pin === true,
            }}
            onFinish={plotChartspaceCharts}
            disabled={isChartspaceDisabled}
          >
            <Form.Item name="per_pin" noStyle>
              <Input type="hidden" />
            </Form.Item>
            <Form.Item name="chart" label="Chart Type">
              <Select
                placeholder="-Select-"
                className="w-36!"
                popupMatchSelectWidth={false}
                labelInValue
                options={
                  component.per_pin === true
                    ? OptionsList.chartspace_chart_types.per_pin
                    : (OptionsList.chartspace_chart_types[
                        pageMeta.analysis_type
                      ] ?? OptionsList.chartspace_chart_types.default)
                }
              />
            </Form.Item>
            {component.per_pin === true && (
              <Form.Item
                name="pin_chart_display"
                label="Pin Chart Display Options"
              >
                <Select
                  placeholder="Pin Chart Display Options"
                  popupMatchSelectWidth={false}
                  labelInValue
                  options={[
                    {
                      value: "per_pin",
                      label: "Per Pin (Individual Charts)",
                    },
                    {
                      value: "multi_pin",
                      label: "Multi-Pin (One Chart)",
                    },
                    {
                      value: "merge_pins",
                      label: "Merge Pins (One Chart)",
                    },
                  ]}
                />
              </Form.Item>
            )}
            <Form.Item name="display_chart_size" label="Layout">
              <Select
                placeholder="-Select-"
                className="w-32!"
                popupMatchSelectWidth={false}
                options={[
                  {
                    value: 1,
                    label: "1 Chart per Row",
                  },
                  {
                    value: 2,
                    label: "2 Charts per Row",
                  },
                  {
                    value: 3,
                    label: "3 Charts per Row",
                  },
                  {
                    value: 4,
                    label: "4 Charts per Row",
                  },
                  {
                    value: 5,
                    label: "5 Charts per Row",
                  },
                ]}
              />
            </Form.Item>
            {component.per_pin !== true && (
              <Form.Item name="stats_type" label="Data Source">
                <Select
                  placeholder="-Select-"
                  className="!w-48"
                  popupMatchSelectWidth={false}
                  options={[
                    {
                      value: "all",
                      label: "All",
                    },
                    {
                      value: "rp",
                      label: "All (Last Results Per Die)",
                    },
                    {
                      value: "pass",
                      label: "Die Passing this Test (Last Results Per Die)",
                    },
                    {
                      value: "passing_unit",
                      label: "Passing Die (Last Results Per Die)",
                    },
                    {
                      value: "iqr",
                      label: "Robust Data (Last Results Per Die)",
                    },
                  ]}
                />
              </Form.Item>
            )}
            {/* <Form.Item name="iqr_n" label="Robust Data N">
              <InputNumber className="w-16" disabled />
            </Form.Item> */}
            {/* <Form.Item name="cs_chart_range" label="Data Range">
              <Select
                placeholder="-Select-"
                className="w-32!"
                popupMatchSelectWidth={false}
                disabled
                options={[
                  {
                    value: "test_limits_20pct",
                    label: "-/+20% Test Limits",
                  },
                  {
                    value: "data_and_limits",
                    label: "Data and Limits",
                  },
                  {
                    value: "test_limits",
                    label: "Test Limits",
                  },
                  {
                    value: "robust_limit",
                    label: "Robust Limits",
                  },
                  {
                    value: "min_max",
                    label: "Data Min & Max",
                  },
                  {
                    value: "overall_min_max",
                    label: "Min & Max of Selection",
                  },
                  {
                    value: "custom_range",
                    label: "Custom Range",
                  },
                ]}
              />
            </Form.Item> */}
            {/* <Form.Item name="min_cs_chart_range" label="Min">
              <InputNumber className="w-16" disabled />
            </Form.Item>
            <Form.Item name="max_cs_chart_range" label="Max">
              <InputNumber className="w-16" disabled />
            </Form.Item> */}
            {component.per_pin !== true && (
              <Form.Item name="show_limits" valuePropName="checked">
                <Checkbox>Show Test Limits</Checkbox>
              </Form.Item>
            )}
            <Form.Item>
              <Tooltip
                title="Please select a test from the table below to plot charts."
                mouseEnterDelay={0.5}
                defaultOpen
              >
                <Button
                  type="primary"
                  onClick={() => chartspaceOptionsForm.submit()}
                >
                  Plot
                </Button>
              </Tooltip>
            </Form.Item>
            <Form.Item>
              <Button onClick={() => chartspaceOptionsForm.resetFields()}>
                Reset
              </Button>
            </Form.Item>
            <Form.Item>
              {/* <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={() => setIsChartspaceChartOptionsOpen(true)}
                disabled={
                  !gridSelectionData[selectionStoreKey] ||
                  gridSelectionData[selectionStoreKey].length === 0
                }
              >
                Advance Settings
              </Button> */}
              <Modal
                width="50%"
                open={isChartspaceChartOptionsOpen}
                onCancel={() => setIsChartspaceChartOptionsOpen(false)}
                footer={[
                  <Button
                    key="cancel"
                    onClick={() => setIsChartspaceChartOptionsOpen(false)}
                  >
                    Cancel
                  </Button>,
                  <Button
                    key="reset"
                    onClick={() =>
                      chartspaceChartOptionsForm.setFieldsValue(
                        chartspaceOptionsForm.getFieldsValue(),
                      )
                    }
                  >
                    Reset
                  </Button>,
                  <Button key="delete" disabled>
                    Delete
                  </Button>,
                  <Button key="save" disabled>
                    Save Changes
                  </Button>,
                  <Button
                    key="plot"
                    type="primary"
                    onClick={() => chartspaceChartOptionsForm.submit()}
                  >
                    Plot
                  </Button>,
                ]}
                centered
                closable
                destroyOnClose
              >
                <ChartspaceChartOptionsForm
                  chartspaceChartOptionsForm={chartspaceChartOptionsForm}
                  onFinish={plotChartspaceCharts}
                  initialValues={chartspaceOptionsForm.getFieldsValue()}
                />
              </Modal>
            </Form.Item>
          </Form>
        </Col>
      </Row>
      <div className="bg-zinc-50 p-2">
        {chartspaceCharts.length > 0 &&
          !Object.keys(chartspaceCharts).every((value) =>
            hiddenChartspaceIndexes.includes(parseInt(value)),
          ) && (
            <Row>
              <Col span={24} className="text-end">
                <Checkbox onChange={onChangeReplaceCharts} defaultChecked>
                  <Text strong>Always Replace</Text> existing charts with new
                  charts{" "}
                  <Tooltip title="Deselecting this will stack future charts below the current one.">
                    <InfoCircleTwoTone />
                  </Tooltip>
                </Checkbox>
              </Col>
            </Row>
          )}
        {chartspaceCharts.map(function (group, index) {
          return (
            <Row key={`chartspace_charts_${index}_wrapper`}>
              <Col span={24}>
                <div
                  className={
                    hiddenChartspaceIndexes.indexOf(index) !== -1
                      ? "hidden"
                      : ""
                  }
                >
                  {renderDivider(index)}
                  <Row>
                    {group.title && (
                      <Col span={12}>
                        <Title level={5} className="!my-2">
                          {group.title}
                        </Title>
                      </Col>
                    )}
                    <Col span={group.title ? 12 : 24} className="text-end">
                      <Space size="middle">
                        <Button
                          type="link"
                          size="large"
                          onClick={() => removeChartsByChartspaceIndex(index)}
                        >
                          <DeleteOutlined />
                        </Button>
                      </Space>
                    </Col>
                  </Row>
                  <Row gutter={[16, 16]}>
                    {Object.keys(group.charts).map((chartKey) => {
                      return (
                        <Col
                          key={`chartspace_chart_${index}_${chartKey}_wrapper`}
                          style={{
                            width: `${100 / chartsPerRow[index]}%`,
                          }}
                        >
                          <div className="border border-solid border-black/[.25]">
                            {React.createElement(
                              ChartspaceChart,
                              chartspaceCharts[index]["charts"][chartKey],
                            )}
                          </div>
                        </Col>
                      );
                    })}
                  </Row>
                </div>
              </Col>
            </Row>
          );
        })}
      </div>
    </div>
  );
}
