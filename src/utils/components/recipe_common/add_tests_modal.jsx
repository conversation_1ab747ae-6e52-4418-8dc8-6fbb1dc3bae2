"use client";

import { <PERSON>pp, <PERSON><PERSON>, Col, Form, Modal, Radio, Row, Typography } from "antd";
import { FileAddOutlined } from "@ant-design/icons";
import { useCallback, useEffect, useState } from "react";
import TestListSelect from "../test_list_select";
import Api from "../../../../src/utils/api";
const { Paragraph, Text } = Typography;

/**
 * Add tests modal component
 *
 * @param {boolean} isModalOpen
 * @param {function} setIsModalOpen
 * @param {function} setTestsToAdd
 * @param {object} testsGridRef
 * @param {function} addTestsCallback
 * @param {object} selectedParams
 * @param {string} defaultTestCategory
 * @returns {JSX.Element}
 */
const AddTestsModal = ({
  selectedParams,
  isModalOpen,
  setIsModalOpen,
  setTestsToAdd,
  testsGridRef,
  addTestsCallback,
  defaultTestCategory = "traceability_tests",
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [isTestSelectDisabled, setIsTestSelectDisabled] = useState(true);
  const [isAddTestsBtnDisabled, setIsAddTestsBtnDisabled] = useState(false);
  const [traceabilityTestsCount, setTraceabilityTestsCount] = useState(0);
  const [totalTestsCount, setTotalTestsCount] = useState(0);
  const [testsLimit, setTestsLimit] = useState(0);
  const [pageFilters, setPageFilters] = useState({});

  useEffect(() => {
    if (isModalOpen) {
      form.resetFields();
      setIsTestSelectDisabled(defaultTestCategory === "all");
      setIsAddTestsBtnDisabled(defaultTestCategory === "select");
      const filters = generatePageFilters();
      // If within the traceability page, always get the tests count
      if (typeof addTestsCallback !== "function") {
        fetchTotalTestsCount(filters);
      }
    }
  }, [isModalOpen]);

  useEffect(() => {
    // If from search, get only the test count if selection changes
    if (typeof addTestsCallback === "function" && pageFilters?.dsk) {
      fetchTotalTestsCount(pageFilters);
    }
  }, [pageFilters.dsk]);

  /**
   * Fetch the total tests count
   *
   * @param {object} filters
   */
  const fetchTotalTestsCount = useCallback(
    (filters) => {
      Api.getTotalTestsCount(
        (res) => {
          if (res.success) {
            setTraceabilityTestsCount(res.data.traceability);
            setTotalTestsCount(res.data.total);
            setTestsLimit(res.data.load_tests_limit);
          } else {
            message.warning(res.message, 5);
          }
        },
        (err) => {
          message.error(err, 5);
        },
        filters,
      );
    },
    [traceabilityTestsCount],
  );

  /**
   * Get the tnums already in the table
   * Optimized to avoid expensive forEachNode operation
   *
   * @returns {string}
   */
  const getExcludeTnums = () => {
    if (!testsGridRef?.current?.api) {
      return "";
    }

    const gridApi = testsGridRef.current.api;
    const rowModel = gridApi.getModel();
    let excludeTnums = [];

    if (rowModel && rowModel.rowsToDisplay) {
      // More efficient: get data directly from row model
      excludeTnums = rowModel.rowsToDisplay.map((row) => row.data.test_number);
    } else {
      // Fallback to forEachNode if direct access isn't available
      gridApi.forEachNode((node) => {
        excludeTnums.push(node.data.test_number);
      });
    }

    return excludeTnums.join(",");
  };

  /**
   * Generate the filters for the page to be generated
   *
   * @returns {object} filters
   */
  const generatePageFilters = () => {
    const filters = { src_type: "dsk" };
    Object.keys(selectedParams)
      .filter((key) => selectedParams[key].length)
      .forEach((key) => {
        filters[key] = Array.isArray(selectedParams[key])
          ? selectedParams[key].join(",")
          : selectedParams[key];
      });
    filters.src_value = filters.dsk;
    filters.tnum_exclude = getExcludeTnums();
    setPageFilters(filters);

    return filters;
  };

  /**
   * Fetch the tests to add in the setup table
   *
   * @param {object} payload
   */
  const getTestToAdd = (payload) => {
    Api.getTraceabilityTestTable(
      (res) => {
        if (res.success) {
          setTestsToAdd(res.data.table_data);
          testsGridRef.current.api.setGridOption("loading", false);
        } else {
          message.warning(res.message, 5);
        }
      },
      (err) => {
        message.error(err, 5);
      },
      payload,
    );
  };

  /**
   * Get the selected test numbers
   *
   * @returns {string} Comma-separated test numbers
   */
  const getTnums = () => {
    return form
      .getFieldValue("tests_to_add")
      .map((tnum) => tnum.split("|")[1])
      .join(",");
  };

  /**
   * When user clicks the add tests button
   */
  const handleAddTest = () => {
    const testCategory = form.getFieldValue("test_category");
    if (typeof addTestsCallback === "function") {
      if (testCategory === "select") {
        Api.getShortUrl(
          (res) => {
            if (res.success) {
              addTestsCallback({ surl: res.data });
            }
          },
          (err) => {
            message.error(err, 5);
          },
          { tnum: getTnums() },
        );
      } else {
        addTestsCallback({
          auto_detect_otp:
            form.getFieldValue("test_category") === "traceability_tests"
              ? "1"
              : "0",
        });
      }
    } else {
      testsGridRef.current.api.setGridOption("loading", true);
      const filters = {
        ...pageFilters,
        auto_detect_otp:
          form.getFieldValue("test_category") === "traceability_tests"
            ? "1"
            : "0",
      };
      const paramsToExclude = ["surl", "recipe_name"];
      paramsToExclude.forEach((param) => {
        if (filters[param]) {
          delete filters[param];
        }
      });
      if (testCategory === "select") {
        filters.tnum = getTnums();
      }
      getTestToAdd(filters);
    }
    setIsModalOpen(false);
  };

  /**
   * Event handler when users cancels the modal
   */
  const closeModal = () => {
    setIsModalOpen(false);
  };

  /**
   * Handles the event where a field changes in the form
   *
   * @param {array} changedFields
   */
  const handleFieldsChange = (changedFields) => {
    if (changedFields[0].name.includes("test_category")) {
      if (changedFields[0].value === "select") {
        setIsTestSelectDisabled(false);
        setIsAddTestsBtnDisabled(
          typeof form.getFieldValue("tests_to_add") === "undefined" ||
            form.getFieldValue("tests_to_add").length === 0,
        );
      } else {
        setIsTestSelectDisabled(true);
        setIsAddTestsBtnDisabled(false);
      }
    } else if (changedFields[0].name.includes("tests_to_add")) {
      setIsAddTestsBtnDisabled(changedFields[0].value.length === 0);
    }
  };

  return (
    <Modal
      title="Add Tests"
      open={isModalOpen}
      onOk={handleAddTest}
      onCancel={closeModal}
      width={"50vw"}
      destroyOnHidden
      footer={[
        <Button key="back" onClick={closeModal}>
          Close
        </Button>,
        <Button
          icon={<FileAddOutlined />}
          key="submit"
          type="primary"
          onClick={handleAddTest}
          disabled={isAddTestsBtnDisabled}
        >
          Add Tests
        </Button>,
      ]}
    >
      <Form
        form={form}
        name="add_tests"
        layout="vertical"
        onFieldsChange={handleFieldsChange}
        initialValues={{ test_category: defaultTestCategory }}
      >
        <Row className="mt-3">
          <Col span={8}>
            <Form.Item name="test_category">
              <Radio.Group className="flex! flex-col! gap-2">
                <Radio
                  value="traceability_tests"
                  disabled={traceabilityTestsCount === 0}
                >
                  Detect Tests Automatically
                </Radio>
                <Radio value="select">Select Tests Manually</Radio>
                <Radio value="all">Add All Tests</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={16}>
            <Text>{`${traceabilityTestsCount.toLocaleString()} Total tests available`}</Text>
            <Form.Item name="tests_to_add" className="my-1!">
              <TestListSelect
                mode="multiple"
                labelInValue={false}
                autoClearSearchValue={false}
                className="max-h-80 overflow-y-auto"
                apiParams={pageFilters}
                disabled={isTestSelectDisabled}
              ></TestListSelect>
            </Form.Item>
            <Text>{`${totalTestsCount.toLocaleString()} Total tests available`}</Text>
          </Col>
        </Row>
      </Form>
      {totalTestsCount > testsLimit && (
        <Paragraph className="border border-solid border-[#FEE99F] bg-[#FEFBE6] p-2">
          <Text strong>Note:</Text> If the total number of selected tests
          exceeds {testsLimit}, only the first {testsLimit} will be loaded at a
          time. To add the remaining tests, repeat the process as needed by
          clicking the <Text keyboard>Add Tests</Text>button again to add the
          next batch of {testsLimit} tests.
        </Paragraph>
      )}
    </Modal>
  );
};

export default AddTestsModal;
