# Traceability Page N+1 Query Optimizations

## Overview
This document outlines the N+1 query issues identified and fixed in the traceability page and its related components.

## Issues Identified and Fixed

### 1. Recipe Data Loading N+1 Issue (`traceability_page.jsx`)

**Problem**: The `loadAppendItems()` function used nested `forEach` loops causing O(n²) complexity.

**Solution**: 
- Replaced nested loops with Map-based lookups for O(1) performance
- Used single-pass algorithm to categorize algorithms
- Optimized variable assignment using Map.get()

```javascript
// Before: O(n²) nested loops
newAppendItems.forEach((newItem) => {
  standardAppendAlgos.forEach((item) => {
    if (newItem.value === item.value) {
      newItem.vars = item.vars;
    }
  });
});

// After: O(n) with Map lookups
const newAppendItems = initialAppendItems.map((item) => {
  const standardAlgo = standardAppendAlgosMap.get(item.value);
  return {
    ...item,
    vars: standardAlgo ? standardAlgo.vars : []
  };
});
```

### 2. Test Variables Processing Optimization

**Problem**: Multiple iterations over test data and inefficient grid operations.

**Solution**:
- Combined multiple data processing loops into single pass
- Replaced individual node selection with `selectAll()` for better performance
- Batched state updates to reduce re-renders

```javascript
// Before: Multiple loops and individual operations
const testNumbers = [];
const rowData = [];
const testsVariables = Object.values(recipeData.test_info).map((obj) => {
  rowData.push(obj);
  testNumbers.push(obj.test_number);
  // ...
});
testsGridRef.current.api.forEachNode((node) => {
  node.setSelected(true);
});

// After: Single loop and batch operations
testInfoValues.forEach((obj) => {
  rowData.push(obj);
  testsVariables.push({
    value: obj.variable,
    label: `${obj.variable} (${actualValue})`,
    actualValue: actualValue,
    tnum: obj.test_number,
  });
});
testsGridRef.current.api.selectAll();
```

### 3. Equation Variables Handling Optimization

**Problem**: Nested loops in equation variable processing causing N+1 issues.

**Solution**:
- Used `flatMap` to eliminate nested `forEach` loops
- Single-pass processing for both options and values
- Batched state updates to minimize re-renders

```javascript
// Before: Nested forEach loops
recipeData.algos.forEach((algo) => {
  algo.vars.forEach((variable) => {
    if (typeof variable.actualValue !== "undefined") {
      equationOptions.push(variable);
      equationValues[variable.value] = {
        value: variable.actualValue,
        result: variable.result,
      };
    }
  });
});

// After: Single flatMap operation
const validVariables = recipeData.algos.flatMap((algo) =>
  algo.vars.filter((variable) => typeof variable.actualValue !== "undefined")
);
validVariables.forEach((variable) => {
  equationOptions.push(variable);
  equationValues[variable.value] = {
    value: variable.actualValue,
    result: variable.result,
  };
});
```

### 4. Grid Data Fetching Optimizations

**Problem**: Expensive `forEachNode` operations throughout the codebase.

**Solution**:
- Implemented direct access to grid row model when available
- Added fallback to `forEachNode` for compatibility
- Used `getSelectedNodes()` for more efficient selection handling

```javascript
// Optimized grid data access pattern
const gridApi = testsGridRef.current.api;
const rowModel = gridApi.getModel();

if (rowModel && rowModel.rowsToDisplay) {
  // More efficient: direct access to row data
  rowData = rowModel.rowsToDisplay.map(row => row.data);
} else {
  // Fallback for compatibility
  gridApi.forEachNode((rowNode) => {
    rowData.push(rowNode.data);
  });
}
```

### 5. Variable Options Update Optimization

**Problem**: Frequent recalculations and unnecessary re-renders.

**Solution**:
- Added memoization using `useMemo` for expensive calculations
- Implemented change detection to prevent unnecessary updates
- Used `useCallback` for stable function references

```javascript
// Memoized variable options
const memoizedVariableOptions = useMemo(() => {
  const appendVariableOptions = getAppendOptions();
  return [
    ...defaultVariableOptions,
    ...appendVariableOptions,
    ...variablesFromTestsTable,
    ...addedEquationVariableOptions,
  ];
}, [defaultVariableOptions, appendItems, variablesFromTestsTable, addedEquationVariableOptions]);
```

## Performance Improvements

1. **Reduced Time Complexity**: Changed O(n²) operations to O(n) or O(1)
2. **Minimized Re-renders**: Batched state updates and added memoization
3. **Optimized Grid Operations**: Reduced expensive DOM traversals
4. **Improved Memory Usage**: Eliminated redundant data structures

## Files Modified

- `app/(main)/(full-width-content)/(pages)/traceability/traceability_page.jsx`
- `app/(main)/(full-width-content)/(pages)/traceability/traceability_equation.jsx`
- `app/(main)/(full-width-content)/(pages)/traceability/traceability_tests_grid.jsx`
- `src/utils/components/recipe_common/add_tests_modal.jsx`

## Testing Recommendations

1. Test with large datasets to verify performance improvements
2. Verify all functionality remains intact after optimizations
3. Monitor for any regression in user experience
4. Test grid operations with various data sizes
